{"boxes": [{"box": {"maxclass": "comment", "text": "Grab and release!", "patching_rect": [890.0, 478.0, 150.0, 20.0], "id": "obj-57", "numinlets": 1, "numoutlets": 0}}, {"box": {"maxclass": "comment", "text": "Send a control name into the same live.object", "linecount": 2, "patching_rect": [750.0, 331.0, 150.0, 34.0], "id": "obj-56", "numinlets": 1, "numoutlets": 0}}, {"box": {"maxclass": "comment", "text": "You can get info on the control surface, and also get the list of control names", "linecount": 3, "patching_rect": [119.0, 496.0, 156.0, 48.0], "id": "obj-54", "numinlets": 1, "numoutlets": 0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl.join", "outlettype": ["", ""], "patching_rect": [500.5, 516.0, 39.0, 22.0], "id": "obj-50", "numinlets": 2, "numoutlets": 2}}, {"box": {"maxclass": "message", "text": "call release_control", "outlettype": [""], "patching_rect": [784.0, 478.0, 112.0, 22.0], "id": "obj-49", "numinlets": 2, "numoutlets": 1}}, {"box": {"maxclass": "message", "text": "call grab_control", "outlettype": [""], "patching_rect": [683.0, 478.0, 97.0, 22.0], "id": "obj-47", "numinlets": 2, "numoutlets": 1}}, {"box": {"maxclass": "message", "text": "Button_Matrix", "outlettype": [""], "patching_rect": [668.5, 331.0, 83.0, 22.0], "id": "obj-38", "numinlets": 2, "numoutlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "outlettype": ["", ""], "patching_rect": [520.5, 478.0, 155.0, 22.0], "id": "obj-35", "numinlets": 2, "numoutlets": 2}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "outlettype": [""], "patching_rect": [520.5, 440.0, 62.0, 22.0], "id": "obj-31", "numinlets": 2, "numoutlets": 1, "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t l l", "outlettype": ["", ""], "patching_rect": [311.5, 372.0, 29.5, 22.0], "id": "obj-30", "numinlets": 1, "numoutlets": 2}}, {"box": {"maxclass": "message", "text": "Scene_Launch_Button0", "outlettype": [""], "patching_rect": [520.5, 331.0, 137.0, 22.0], "id": "obj-29", "numinlets": 2, "numoutlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend call get_control_by_name", "outlettype": [""], "patching_rect": [520.5, 372.0, 193.0, 22.0], "id": "obj-27", "numinlets": 1, "numoutlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print control_names", "patching_rect": [337.5, 516.0, 114.0, 22.0], "id": "obj-20", "numinlets": 1, "numoutlets": 0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route info get_control_names", "outlettype": ["", "", ""], "patching_rect": [271.5, 440.0, 164.0, 22.0], "id": "obj-19", "numinlets": 3, "numoutlets": 3}}, {"box": {"maxclass": "message", "text": "getinfo", "outlettype": [""], "patching_rect": [119.0, 331.0, 45.0, 22.0], "id": "obj-18", "numinlets": 2, "numoutlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print info", "patching_rect": [271.5, 516.0, 55.0, 22.0], "id": "obj-15", "numinlets": 1, "numoutlets": 0}}, {"box": {"maxclass": "message", "text": "call get_control_names", "outlettype": [""], "patching_rect": [176.0, 331.0, 132.0, 22.0], "id": "obj-14", "numinlets": 2, "numoutlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "outlettype": [""], "patching_rect": [271.5, 402.0, 62.0, 22.0], "id": "obj-12", "numinlets": 2, "numoutlets": 1, "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "outlettype": ["", "", ""], "patching_rect": [311.5, 331.0, 53.0, 22.0], "id": "obj-11", "numinlets": 1, "numoutlets": 3}}, {"box": {"maxclass": "message", "text": "Push2", "outlettype": [""], "patching_rect": [311.5, 247.0, 43.0, 22.0], "id": "obj-5", "numinlets": 2, "numoutlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "p ControlSurfaceID", "outlettype": [""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [311.5, 291.0, 102.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-82", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0, "patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [926.0, 79.0, 291.0, 770.0], "bglocked": 0, "openinpresentation": 0, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "outlettype": ["bang", ""], "patching_rect": [82.5, 152.0, 30.0, 22.0], "id": "obj-5", "numinlets": 1, "numoutlets": 2}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend path control_surfaces", "outlettype": [""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [130.0, 692.0, 157.0, 20.0], "fontface": 0, "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-134", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route int", "outlettype": ["", ""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [130.0, 664.414551, 50.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-61", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl filter Push", "outlettype": ["", ""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [130.0, 633.414551, 70.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-60", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl group 8", "outlettype": ["", ""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [130.0, 576.414551, 58.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-56", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "listfunnel", "outlettype": ["list"], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [130.0, 605.414551, 55.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-51", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route type", "outlettype": ["", ""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [130.0, 545.414551, 59.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-8", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "outlettype": ["bang", ""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [154.5, 461.414551, 34.5, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-26", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "gettype", "outlettype": [""], "fontname": "Arial Bold", "patching_rect": [130.0, 488.414551, 46.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-24", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "gradient": 0, "bgcolor": [0.867, 0.867, 0.867, 1.0], "bgfillcolor_type": "gradient", "bgfillcolor_color1": [0.867, 0.867, 0.867, 1.0], "bgfillcolor_color2": [0.685, 0.685, 0.685, 1.0], "bgfillcolor_color": [0.290196, 0.309804, 0.301961, 1.0], "bgfillcolor_autogradient": 0.79, "bgfillcolor_angle": 270.0, "bgfillcolor_proportion": 0.39}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "outlettype": [""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [130.0, 512.414551, 59.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-46", "color": [0.984314, 0.819608, 0.05098, 1.0], "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "outlettype": ["", "", ""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [154.5, 436.414551, 51.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-20", "color": [0.984314, 0.819608, 0.05098, 1.0], "numinlets": 1, "numoutlets": 3, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend path control_surfaces", "outlettype": [""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [154.5, 409.414551, 157.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-49", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "0", "outlettype": [""], "fontname": "Arial Bold", "patching_rect": [234.5, 351.414551, 32.5, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-50", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "gradient": 0, "bgcolor": [0.867, 0.867, 0.867, 1.0], "bgfillcolor_type": "gradient", "bgfillcolor_color1": [0.867, 0.867, 0.867, 1.0], "bgfillcolor_color2": [0.685, 0.685, 0.685, 1.0], "bgfillcolor_color": [0.290196, 0.309804, 0.301961, 1.0], "bgfillcolor_autogradient": 0.79, "bgfillcolor_angle": 270.0, "bgfillcolor_proportion": 0.39}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "counter", "outlettype": ["int", "", "", "int"], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [154.5, 381.414551, 117.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-14", "numinlets": 5, "numoutlets": 4, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "uzi", "outlettype": ["bang", "bang", "int"], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [154.5, 351.414551, 57.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-15", "numinlets": 2, "numoutlets": 3, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route control_surfaces", "outlettype": ["", ""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [154.5, 273.414551, 119.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-40", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t i b", "outlettype": ["int", "bang"], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [154.5, 321.414551, 99.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-16", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route count", "outlettype": ["", ""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [154.5, 249.414551, 65.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-17", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "getcount control_surfaces", "outlettype": [""], "fontname": "Arial Bold", "patching_rect": [82.5, 185.414551, 136.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-41", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "gradient": 0, "bgcolor": [0.867, 0.867, 0.867, 1.0], "bgfillcolor_type": "gradient", "bgfillcolor_color1": [0.867, 0.867, 0.867, 1.0], "bgfillcolor_color2": [0.685, 0.685, 0.685, 1.0], "bgfillcolor_color": [0.290196, 0.309804, 0.301961, 1.0], "bgfillcolor_autogradient": 0.79, "bgfillcolor_angle": 270.0, "bgfillcolor_proportion": 0.39}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "outlettype": ["", "", ""], "fontname": "Arial Bold", "bgcolor": [1.0, 1.0, 1.0, 1.0], "patching_rect": [82.5, 225.414551, 91.0, 20.0], "textcolor": [0.0, 0.019608, 0.078431, 1.0], "id": "obj-22", "color": [0.984314, 0.819608, 0.05098, 1.0], "numinlets": 1, "numoutlets": 3, "fontsize": 10.0}}, {"box": {"maxclass": "inlet", "outlettype": [""], "patching_rect": [82.5, 31.414551, 25.0, 25.0], "id": "obj-6", "numinlets": 0, "numoutlets": 1, "comment": "", "index": 1}}, {"box": {"maxclass": "outlet", "patching_rect": [130.0, 725.414551, 25.0, 25.0], "id": "obj-7", "numinlets": 1, "numoutlets": 0, "comment": "", "index": 1}}], "lines": [{"patchline": {"source": ["obj-8", 0], "destination": ["obj-56", 0]}}, {"patchline": {"source": ["obj-61", 0], "destination": ["obj-134", 0]}}, {"patchline": {"source": ["obj-60", 0], "destination": ["obj-61", 0]}}, {"patchline": {"source": ["obj-6", 0], "destination": ["obj-5", 0]}}, {"patchline": {"source": ["obj-56", 0], "destination": ["obj-51", 0]}}, {"patchline": {"source": ["obj-51", 0], "destination": ["obj-60", 0]}}, {"patchline": {"source": ["obj-50", 0], "destination": ["obj-14", 2]}}, {"patchline": {"source": ["obj-5", 1], "destination": ["obj-60", 1], "midpoints": [103.0, 180.0, 69.0, 180.0, 69.0, 627.0, 190.5, 627.0]}}, {"patchline": {"source": ["obj-5", 0], "destination": ["obj-41", 0]}}, {"patchline": {"source": ["obj-49", 0], "destination": ["obj-20", 0]}}, {"patchline": {"source": ["obj-46", 0], "destination": ["obj-8", 0]}}, {"patchline": {"source": ["obj-41", 0], "destination": ["obj-22", 0]}}, {"patchline": {"source": ["obj-40", 0], "destination": ["obj-56", 1], "midpoints": [164.0, 308.414551, 322.0, 308.414551, 322.0, 562.414551, 178.5, 562.414551], "order": 0}}, {"patchline": {"source": ["obj-40", 0], "destination": ["obj-16", 0], "order": 1}}, {"patchline": {"source": ["obj-26", 1], "destination": ["obj-46", 1]}}, {"patchline": {"source": ["obj-26", 0], "destination": ["obj-24", 0]}}, {"patchline": {"source": ["obj-24", 0], "destination": ["obj-46", 0]}}, {"patchline": {"source": ["obj-22", 2], "destination": ["obj-17", 0]}}, {"patchline": {"source": ["obj-20", 0], "destination": ["obj-26", 0]}}, {"patchline": {"source": ["obj-17", 0], "destination": ["obj-40", 0]}}, {"patchline": {"source": ["obj-16", 1], "destination": ["obj-50", 0]}}, {"patchline": {"source": ["obj-16", 0], "destination": ["obj-15", 0]}}, {"patchline": {"source": ["obj-15", 0], "destination": ["obj-14", 0]}}, {"patchline": {"source": ["obj-14", 0], "destination": ["obj-49", 0]}}, {"patchline": {"source": ["obj-134", 0], "destination": ["obj-7", 0]}}]}, "saved_object_attributes": {"description": "", "digest": "", "globalpatchername": "", "tags": ""}}}], "lines": [{"patchline": {"source": ["obj-5", 0], "destination": ["obj-82", 0]}}, {"patchline": {"source": ["obj-82", 0], "destination": ["obj-11", 0]}}, {"patchline": {"source": ["obj-14", 0], "destination": ["obj-12", 0]}}, {"patchline": {"source": ["obj-18", 0], "destination": ["obj-12", 0]}}, {"patchline": {"source": ["obj-12", 0], "destination": ["obj-19", 0]}}, {"patchline": {"source": ["obj-19", 0], "destination": ["obj-15", 0]}}, {"patchline": {"source": ["obj-19", 1], "destination": ["obj-20", 0]}}, {"patchline": {"source": ["obj-29", 0], "destination": ["obj-27", 0]}}, {"patchline": {"source": ["obj-30", 0], "destination": ["obj-12", 1]}}, {"patchline": {"source": ["obj-11", 0], "destination": ["obj-30", 0]}}, {"patchline": {"source": ["obj-30", 1], "destination": ["obj-31", 1]}}, {"patchline": {"source": ["obj-27", 0], "destination": ["obj-31", 0]}}, {"patchline": {"source": ["obj-31", 0], "destination": ["obj-35", 0]}}, {"patchline": {"source": ["obj-38", 0], "destination": ["obj-27", 0]}}, {"patchline": {"source": ["obj-50", 0], "destination": ["obj-31", 0], "midpoints": [510.0, 537.0, 486.0, 537.0, 486.0, 435.0, 530.0, 435.0]}}, {"patchline": {"source": ["obj-35", 0], "destination": ["obj-50", 1]}}, {"patchline": {"source": ["obj-49", 0], "destination": ["obj-50", 0], "midpoints": [793.5, 510.0, 510.0, 510.0]}}, {"patchline": {"source": ["obj-47", 0], "destination": ["obj-50", 0], "midpoints": [692.5, 510.0, 510.0, 510.0]}}], "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box"}