from __future__ import absolute_import, print_function, unicode_literals
from _Framework.ControlSurface import ControlSurface
from _Framework.ButtonElement import ButtonElement
from _Framework.InputControlElement import MIDI_CC_TYPE, MIDI_NOTE_TYPE
from _Framework.SessionComponent import SessionComponent

class ClipQueueController(ControlSurface):
    def __init__(self, c_instance):
        super(ClipQueueController, self).__init__(c_instance)
        self._queued_clips = []
        self.log_message("ClipQueueController initialized")
        
        with self.component_guard():
            self._setup_session_control()
            self._setup_transport_control()
    
    def _setup_session_control(self):
        # Create a session component that mirrors the Push 2 session grid
        self._session = SessionComponent(8, 8)
        self._session.set_offsets(0, 0)
        
    def _setup_transport_control(self):
        # Listen for transport play button
        self._play_button = ButtonElement(True, MIDI_CC_TYPE, 0, 85)
        self._play_button.add_value_listener(self._on_play_value)
        
    def _on_play_value(self, value):
        if value:
            self._launch_queued_clips()
            
    def _launch_queued_clips(self):
        if not self._queued_clips:
            return
            
        self.log_message("Launching {} queued clips".format(len(self._queued_clips)))
        for track, scene in self._queued_clips:
            clip_slot = self._session.scene(scene).clip_slot(track)
            if clip_slot and clip_slot.has_clip:
                clip_slot.fire()
        self._queued_clips = []
        
    def receive_midi(self, midi_bytes):
        # Process MIDI from Max for Live device
        status_byte = midi_bytes[0] & 240
        channel = midi_bytes[0] & 15
        
        # Custom protocol for queueing clips
        # Example: Note On messages on channel 15 represent clip queue commands
        # where the note number encodes track (bits 0-3) and scene (bits 4-7)
        if status_byte == 144 and channel == 15:  # Note On, channel 16 (zero-indexed as 15)
            note = midi_bytes[1]
            velocity = midi_bytes[2]
            
            if velocity > 0:  # Note On with velocity
                track = note & 15  # Lower 4 bits for track
                scene = (note >> 4) & 15  # Upper 4 bits for scene
                self._queue_clip(track, scene)
                
        super(ClipQueueController, self).receive_midi(midi_bytes)
        
    def _queue_clip(self, track, scene):
        if (track, scene) not in self._queued_clips:
            self._queued_clips.append((track, scene))
            self.log_message("Queued clip at track {}, scene {}".format(track, scene))
            
            # Send MIDI feedback to Max device
            self._send_midi((176, 0, len(self._queued_clips)))  # CC 0 with count of queued clips