# Research Sources and References for Ableton Internal Queuing

## Original Research Path

This document tracks the original research that led to discovering <PERSON><PERSON><PERSON>'s internal clip queuing system using `set_fire_button_state` and related Live Object Model properties.

## Initial Problem
We were trying to achieve perfectly synchronized clip launching in Ableton Live using Max for Live. The goal was to queue clips (with visual pulsing feedback) and then launch them all simultaneously with zero delay.

## Research Queries and Findings

### 1. Initial Search for Ableton Live Object Model and Queuing
**Query:** `"Ableton Live Object Model clip queued state pulsing internal queuing system Max for Live"`

**Purpose:** Looking for information about <PERSON><PERSON><PERSON>'s built-in clip queuing mechanisms and how to access them via the Live Object Model.

### 2. Search for Specific Clip State Properties
**Query:** `"Live Object Model clip \"fire_state\" \"clip_state\" \"will_record_on_start\" queued state Max for Live"`

**Purpose:** Searching for specific property names that might control clip states, particularly looking for "fire_state" and "will_record_on_start" properties.

### 3. Focused Search on Fire State Properties
**Query:** `"\"fire_state\" \"will_record_on_start\" clip state Ableton Live Object Model"`

**Purpose:** Narrowing down the search to specific properties that might control the queued/triggered state of clips.

### 4. Search for Fire State and Queued Launch Behavior
**Query:** `"Ableton \"fire_state\" clip property \"will_record_on_start\" queued launch state"`

**Purpose:** Looking for documentation on how these properties relate to the visual queued state (pulsing) of clips.

### 5. Max for Live Specific Documentation Search
**Query:** `"Max for Live live.object clip properties fire_state will_record_on_start"`

**Purpose:** Finding Max for Live specific documentation about accessing these clip properties.

## Key Documentation Source

### Primary Source: Cycling74 Live Object Model Documentation
**URL:** https://docs.cycling74.com/legacy/max8/vignettes/live_object_model

**Key Findings from this source:**

#### ClipSlot Properties:
- `will_record_on_start` - "1 = clip slot will record on start"
- `is_triggered` - "1 = clip slot button (Clip Launch, Clip Stop or Clip Record) or button of contained clip are blinking"

#### ClipSlot Functions:
- `set_fire_button_state` - "1 = Live simulates pressing of Clip Launch button until the state is set to 0 or until the slot is stopped otherwise"

#### Clip Properties:
- `will_record_on_start` - "1 for MIDI clips which are in triggered state, with the track armed and MIDI Arrangement Overdub on"
- `is_triggered` - "1 = Clip Launch button is blinking"

#### Clip Functions:
- `set_fire_button_state` - "If the state is set to 1, Live simulates pressing the clip start button until the state is set to 0, or until the clip is otherwise stopped"

## Critical Discovery

The `set_fire_button_state` function was identified as the key to achieving Ableton's internal queuing system. This function:

1. **For Queuing (state = 1):** Simulates pressing the clip launch button and keeps it in a "pressed" state, which creates the pulsing visual feedback that indicates a clip is queued for launch.

2. **For Launching (state = 0):** Releases the simulated button press, which triggers the actual launch of the queued clip in perfect synchronization with Ableton's internal timing.

## Implementation Strategy

Based on this research, the implementation strategy became:

1. **Queue Phase:** Use `set_fire_button_state 1` on ClipSlots to put clips into the pulsing/queued state
2. **Launch Phase:** Use `set_fire_button_state 0` on all queued ClipSlots to release them simultaneously
3. **Verification:** Monitor the `is_triggered` property to confirm clips are properly queued

## Alternative Approaches Considered

During development, we also tested:
- Using `fire()` method directly (immediate launch, no queuing)
- Using `fire()` for queuing and `set_fire_button_state 0` for release (hybrid approach)

## Current Implementation Status

As of the latest implementation (clipLauncher.js v3.0), we're using:
- `fire()` method first for queuing (as it may be more reliable for creating the pulsing state)
- `set_fire_button_state 0` for releasing queued clips
- Fallback mechanisms for both queuing and releasing

## Related Files

- `clipLauncher.js` - Main implementation using these research findings
- `ableton_internal_queuing_research.md` - Detailed analysis of the queuing system
- `debug_guide.md` - Testing procedures based on this research
- `critical_fixes_summary.md` - Summary of fixes implemented using this approach

## Notes for Future Reference

If issues persist with the current implementation, revisiting the Cycling74 Live Object Model documentation may provide additional insights. The documentation is comprehensive and may contain other relevant properties or methods not yet explored.

The key insight from this research is that Ableton Live has built-in mechanisms for clip queuing that can be accessed through the Live Object Model, eliminating the need for custom timing or synchronization code.
