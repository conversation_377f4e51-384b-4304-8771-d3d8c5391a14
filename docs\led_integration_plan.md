# LED Integration Plan - clipQueue.maxpat

## 🎯 **GOAL**
Keep session clip lights visible while holding Session button in clipQueue device. Currently, everything goes dark.

## 🔬 **RESEARCH COMPLETE**
- ✅ Created working LED control system in `led-control-reference.maxpat`
- ✅ Discovered correct Push 2 MIDI color values from official documentation
- ✅ Solved LED connection and timing issues

## 📋 **KEY FINDINGS**

### LED Control Requirements
1. **Button_Matrix Control:** Must be grabbed before LED commands work
2. **Timing Critical:** 300ms delay needed before sending LED commands
3. **Gate System:** Prevents LED commands until Push 2 is ready
4. **Connection Path:** `s ---LEDControl` → `r ---LEDControl` → `prepend call send_midi 144` → `gate` → `live.object`

### Official Push 2 MIDI Values
- **Red:** velocity 127
- **Green:** velocity 126  
- **Blue:** velocity 125
- **Off:** velocity 0
- **MIDI Notes:** 36 = Pad(0,7), 37 = Pad(1,7), etc.

## 🔧 **INTEGRATION STEPS**

### 1. Copy LED System from Reference
Copy these objects from `led-control-reference.maxpat` to `clipQueue.maxpat`:
- `r ---LEDControl` receiver
- `prepend call send_midi 144` formatter
- `gate 1 0` with timing control
- Connect to existing `live.object` (left inlet for LED commands)

### 2. Add LED Commands to Session Mode
When Session button held:
1. Query clip states via Live API
2. Send LED commands based on clip availability:
   - Green (126): Available stopped clips
   - Red (127): Playing clips  
   - Off (0): Empty slots

### 3. Restore LEDs on Session Release
When Session button released:
- Send "off" commands to all custom LEDs
- Allow Push 2 to resume normal LED control

## 📄 **FILES READY**
- `led-control-reference.maxpat` - Complete working LED system
- `clipQueue.maxpat` - Main device ready for LED integration
- Push 2 infrastructure already exists (live.path, s ---PushPath, etc.)

## ⚡ **NEXT ACTION**
Integrate LED control system into clipQueue.maxpat Session mode functionality.
