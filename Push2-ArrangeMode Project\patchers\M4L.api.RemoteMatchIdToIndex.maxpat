{"patcher": {"fileversion": 1, "appversion": {"major": 5, "minor": 1, "revision": 9}, "rect": [832.0, 55.0, 248.0, 276.0], "bglocked": 0, "defrect": [832.0, 55.0, 248.0, 276.0], "openrect": [0.0, 0.0, 0.0, 0.0], "openinpresentation": 0, "default_fontsize": 10.0, "default_fontface": 0, "default_fontname": "Arial Bold", "gridonopen": 0, "gridsize": [8.0, 8.0], "gridsnaponopen": 0, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "boxes": [{"box": {"maxclass": "comment", "text": "needs an index in the format \"id 1\" in the right input and a list of indices in the format \"id 1 id 2 id 3\" in the left input and returns the position of the index in the list or, if not found, a bang at the right outlet.", "linecount": 5, "frgb": [0.101961, 0.121569, 0.172549, 1.0], "id": "obj-12", "fontname": "<PERSON><PERSON>", "patching_rect": [16.0, 32.0, 224.0, 64.0], "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "A helper patch to convert an id to an index.", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "id": "obj-11", "fontname": "Arial Bold", "patching_rect": [16.0, 16.0, 216.0, 18.0], "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "outlet", "id": "obj-9", "patching_rect": [112.0, 232.0, 18.0, 18.0], "numinlets": 1, "numoutlets": 0, "comment": ""}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 0", "outlettype": ["bang", ""], "id": "obj-8", "fontname": "Arial Bold", "patching_rect": [112.0, 200.0, 35.0, 18.0], "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "- 1", "outlettype": ["int"], "id": "obj-4", "fontname": "Arial Bold", "patching_rect": [72.0, 200.0, 34.0, 18.0], "numinlets": 2, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl 666 sub", "outlettype": ["", ""], "id": "obj-7", "fontname": "Arial Bold", "patching_rect": [72.0, 176.0, 59.0, 18.0], "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route id", "outlettype": ["", ""], "id": "obj-6", "fontname": "Arial Bold", "patching_rect": [112.0, 144.0, 47.0, 18.0], "numinlets": 1, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl 666 delace", "outlettype": ["", ""], "id": "obj-5", "fontname": "Arial Bold", "patching_rect": [16.0, 144.0, 75.0, 18.0], "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "inlet", "outlettype": [""], "id": "obj-3", "patching_rect": [16.0, 112.0, 18.0, 18.0], "numinlets": 0, "numoutlets": 1, "comment": ""}}, {"box": {"maxclass": "inlet", "outlettype": [""], "id": "obj-2", "patching_rect": [112.0, 112.0, 18.0, 18.0], "numinlets": 0, "numoutlets": 1, "comment": ""}}, {"box": {"maxclass": "outlet", "id": "obj-1", "patching_rect": [72.0, 232.0, 18.0, 18.0], "numinlets": 1, "numoutlets": 0, "comment": ""}}], "lines": [{"patchline": {"source": ["obj-7", 0], "destination": ["obj-4", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-7", 1], "destination": ["obj-8", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-6", 0], "destination": ["obj-7", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-5", 1], "destination": ["obj-7", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-3", 0], "destination": ["obj-5", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-1", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-9", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-2", 0], "destination": ["obj-6", 0], "hidden": 0, "midpoints": []}}]}}