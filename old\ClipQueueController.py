from __future__ import absolute_import, print_function, unicode_literals
from _Framework.ControlSurface import ControlSurface
from _Framework.MidiMap import MidiMap
from _Framework.ButtonElement import ButtonElement
from _Framework.InputControlElement import MIDI_CC_TYPE, MIDI_NOTE_TYPE
from _Framework.SessionComponent import SessionComponent

class ClipQueueController(ControlSurface):
    def __init__(self, c_instance):
        super(ClipQueueController, self).__init__(c_instance)
        self._queued_clips = []
        self._session_button_pressed = False
        self.log_message("ClipQueueController initialized")
        
        with self.component_guard():
            self._setup_session_control()
            self._setup_transport_control()
    
    def _setup_session_control(self):
        # Create a session component that mirrors the Push 2 session grid
        self._session = SessionComponent(8, 8)
        self._session.set_offsets(0, 0)
        
        # We don't assign buttons here as we're just using this to access clip slots
        
    def _setup_transport_control(self):
        # Listen for transport play button
        self._play_button = ButtonElement(True, MIDI_CC_TYPE, 0, 85)
        self._play_button.add_value_listener(self._on_play_value)
        
    def _on_play_value(self, value):
        if value:
            self._launch_queued_clips()
            
    def _launch_queued_clips(self):
        if not self._queued_clips:
            return
            
        self.log_message("Launching {} queued clips".format(len(self._queued_clips)))
        for track, scene in self._queued_clips:
            clip_slot = self._session.scene(scene).clip_slot(track)
            if clip_slot and clip_slot.has_clip:
                clip_slot.fire()
        self._queued_clips = []
        
    def receive_midi(self, midi_bytes):
        # Process incoming MIDI from Push 2
        # This would need to be customized based on the specific MIDI messages sent by Push 2
        super(ClipQueueController, self).receive_midi(midi_bytes)