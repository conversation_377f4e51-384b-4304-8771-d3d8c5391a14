{"patcher": {"fileversion": 1, "appversion": {"major": 7, "minor": 0, "revision": 5, "architecture": "x64", "modernui": 1}, "rect": [151.0, 86.0, 1075.0, 772.0], "bglocked": 0, "openinpresentation": 0, "default_fontsize": 10.0, "default_fontface": 0, "default_fontname": "Arial Bold", "gridonopen": 1, "gridsize": [8.0, 8.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-63", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 584.0, 190.0, 18.0], "style": "", "text": "Sets the value of the object's property"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-65", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 568.0, 52.0, 18.0], "style": "", "text": "anything"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-108", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [600.0, 160.0, 64.0, 31.0], "style": "", "text": "substitute bang 0"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-62", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 248.0, 62.0, 20.0], "style": "", "text": "prepend id"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [520.0, 160.0, 67.0, 20.0], "style": "", "text": "substitute 0"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-78", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [963.0, 320.0, 32.0, 18.0], "style": "", "text": "Path"}}, {"box": {"comment": "Path", "id": "obj-81", "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [944.0, 320.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"color": [0.984314, 0.819608, 0.05098, 1.0], "fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-76", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [944.0, 288.0, 59.0, 20.0], "saved_object_attributes": {"_persistence": 0}, "style": "", "text": "live.object"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-70", "linecount": 2, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [904.0, 344.0, 120.0, 29.0], "style": "", "text": "ID and path data to be passed to other objects"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-72", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [923.0, 320.0, 20.0, 18.0], "style": "", "text": "ID"}}, {"box": {"comment": "ID", "id": "obj-73", "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [904.0, 320.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"id": "obj-59", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [328.0, 344.0, 18.0, 18.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-56", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [328.0, 368.0, 42.5, 20.0], "style": "", "text": "gate"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-38", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [704.0, 712.0, 37.0, 18.0], "style": "", "text": "Value"}}, {"box": {"comment": "Value", "id": "obj-40", "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [680.0, 712.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [488.0, 672.0, 67.0, 20.0], "style": "", "text": "prepend set"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-50", "linecount": 2, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [411.0, 160.0, 32.0, 29.0], "style": "", "text": "Auto get"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-49", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [440.0, 328.0, 34.5, 20.0], "style": "", "text": "t b l"}}, {"box": {"fontname": "Arial Bold Italic", "fontsize": 11.0, "id": "obj-178", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [544.0, 48.0, 478.0, 19.0], "style": "", "text": "Simplified control and/or observation of Live object properties using the Live API"}}, {"box": {"fontname": "Arial Bold Italic", "fontsize": 18.0, "id": "obj-176", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [544.0, 24.0, 125.0, 27.0], "style": "", "text": "Live.property", "textcolor": [0.301961, 0.337255, 0.403922, 1.0]}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-16", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [848.0, 424.0, 37.0, 18.0], "style": "", "text": "Value"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-20", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [800.0, 440.0, 152.0, 20.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-112", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [88.0, 114.0, 80.0, 18.0], "style": "", "text": "Arg1: property"}}, {"box": {"fontname": "Arial Bold Italic", "fontsize": 12.0, "id": "obj-109", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [96.0, 272.0, 68.0, 20.0], "style": "", "text": "Messages", "textcolor": [0.301961, 0.337255, 0.403922, 1.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-105", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 552.0, 176.0, 18.0], "style": "", "text": "Same as anything (for textedit object)"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-106", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 536.0, 30.0, 18.0], "style": "", "text": "text"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-104", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 520.0, 190.0, 18.0], "style": "", "text": "monitor the value of the object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-103", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 504.0, 52.0, 18.0], "style": "", "text": "observe"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-101", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 488.0, 122.0, 18.0], "style": "", "text": "The path of the object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-102", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 472.0, 32.0, 18.0], "style": "", "text": "path"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-99", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 456.0, 103.0, 18.0], "style": "", "text": "The id of the object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-100", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 440.0, 19.0, 18.0], "style": "", "text": "id"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-98", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [328.0, 472.0, 52.0, 18.0], "style": "", "text": "Property"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-97", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [312.0, 488.0, 128.0, 20.0], "style": "", "text": "warping"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-95", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 304.0, 35.0, 18.0], "style": "", "text": "bang"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-93", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [856.0, 136.0, 56.0, 18.0], "style": "", "text": "Anything"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-92", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [776.0, 152.0, 30.0, 18.0], "style": "", "text": "Text"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-90", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [840.0, 352.0, 19.0, 20.0], "style": "", "text": "t l"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-88", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [856.0, 656.0, 138.0, 18.0], "style": "", "text": "Messages from live.observer"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-89", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [856.0, 672.0, 152.0, 20.0], "style": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-86", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [856.0, 576.0, 127.0, 18.0], "style": "", "text": "Messages to live.observer"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-87", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [856.0, 592.0, 152.0, 20.0], "style": "", "text": "property warping"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-85", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [592.0, 288.0, 44.0, 20.0], "style": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-83", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [280.0, 656.0, 126.0, 18.0], "style": "", "text": "Messages from live.object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-84", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [280.0, 672.0, 152.0, 20.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-82", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [704.0, 296.0, 32.0, 18.0], "style": "", "text": "Path"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-80", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [640.0, 288.0, 20.0, 18.0], "style": "", "text": "ID"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-79", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [499.0, 136.0, 20.0, 18.0], "style": "", "text": "ID"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-77", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [592.0, 312.0, 232.0, 20.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-75", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [456.0, 423.0, 19.0, 20.0], "style": "", "text": "t l"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-74", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [520.0, 136.0, 40.0, 20.0], "prototypename": "Live", "style": "", "triscale": 0.75}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [904.0, 265.0, 99.0, 20.0], "style": "", "text": "t l getpath l"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [760.0, 136.0, 62.0, 20.0], "style": "", "text": "route bang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-71", "linecount": 2, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 408.0, 193.0, 29.0], "style": "", "text": "The property of the object you want to get, set or observe"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-68", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 392.0, 52.0, 18.0], "style": "", "text": "property"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-67", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [280.0, 600.0, 114.0, 18.0], "style": "", "text": "Messages to live.object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-66", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [280.0, 616.0, 152.0, 20.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-64", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [456.0, 568.0, 19.0, 20.0], "style": "", "text": "t l"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-61", "linecount": 3, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 352.0, 192.0, 40.0], "style": "", "text": "<get 1> allows auto-get-value when the abstraction loads or when the target object's id or path changes."}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-60", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 336.0, 27.0, 18.0], "style": "", "text": "get"}}, {"box": {"blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "id": "obj-58", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [360.0, 160.0, 18.0, 18.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-57", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [332.0, 160.0, 27.0, 18.0], "style": "", "text": "Get"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-55", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 320.0, 80.0, 18.0], "style": "", "text": "Same as <get>"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-54", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [244.0, 136.0, 35.0, 18.0], "style": "", "text": "<PERSON>"}}, {"box": {"blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "id": "obj-53", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [280.0, 136.0, 18.0, 18.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-52", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [164.0, 136.0, 35.0, 18.0], "style": "", "text": "Done"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-48", "linecount": 3, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [164.0, 155.0, 92.0, 40.0], "style": "", "text": "Sends a bang when all attributes are loaded"}}, {"box": {"blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "id": "obj-47", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [200.0, 136.0, 18.0, 18.0], "style": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-26", "linecount": 3, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [320.0, 24.0, 193.0, 40.0], "style": "", "text": "< For clarity and convenience, you may want to use one inlet to set messages, and another inlet to set values."}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [704.0, 440.0, 36.5, 20.0], "style": "", "text": "t b l"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-45", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [352.0, 343.0, 47.0, 20.0], "style": "", "text": "select 1"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [392.0, 368.0, 32.5, 20.0], "style": "", "text": "gate"}}, {"box": {"id": "obj-32", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [392.0, 160.0, 18.0, 18.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [360.0, 136.0, 83.0, 20.0], "style": "", "text": "route bang int"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 9, "numoutlets": 9, "outlettype": ["", "", "", "", "", "", "", "", ""], "patching_rect": [200.0, 112.0, 659.0, 20.0], "style": "", "text": "route done bang get property id path observe text"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 288.0, 62.0, 20.0], "style": "", "text": "prepend id"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-39", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [299.0, 440.0, 27.0, 18.0], "style": "", "text": "Get"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-35", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [699.0, 136.0, 51.0, 18.0], "style": "", "text": "Observe"}}, {"box": {"id": "obj-30", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [680.0, 136.0, 18.0, 18.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-37", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [680.0, 473.0, 32.5, 20.0], "style": "", "text": "i"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [88.0, 64.0, 52.0, 20.0], "style": "", "text": "zl slice 1"}}, {"box": {"comment": "ID, path", "id": "obj-19", "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [296.0, 24.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [88.0, 95.0, 93.0, 20.0], "style": "", "text": "prepend property"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-5", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [600.0, 224.0, 74.0, 20.0], "style": "", "text": "prepend path"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-4", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [64.0, 21.0, 124.0, 18.0], "style": "", "text": "Attributes (= messages)"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [88.0, 40.0, 68.0, 20.0], "style": "", "text": "patcherargs"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-34", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [512.0, 712.0, 61.0, 18.0], "style": "", "text": "Value (set)"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-33", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["bang", "int", ""], "patching_rect": [520.0, 351.0, 276.5, 20.0], "style": "", "text": "t b 1 l"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [456.0, 464.0, 67.0, 20.0], "style": "", "text": "prepend set"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-27", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [219.0, 24.0, 76.0, 18.0], "style": "", "text": "< Messages >"}}, {"box": {"color": [0.984314, 0.819608, 0.05098, 1.0], "fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [600.0, 248.0, 53.0, 20.0], "style": "", "text": "live.path"}}, {"box": {"blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "id": "obj-23", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [280.0, 440.0, 18.0, 18.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-22", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [456.0, 536.0, 67.0, 20.0], "style": "", "text": "prepend set"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [456.0, 504.0, 50.0, 20.0], "style": "", "text": "prepend"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-12", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [680.0, 552.0, 61.0, 20.0], "style": "", "text": "zl reg"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [280.0, 551.0, 68.0, 20.0], "style": "", "text": "prepend get"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-6", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [280.0, 528.0, 37.0, 20.0], "style": "", "text": "zl reg"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-18", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [456.0, 632.0, 51.0, 20.0], "style": "", "text": "zl slice 1"}}, {"box": {"color": [0.984314, 0.819608, 0.05098, 1.0], "fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-21", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [456.0, 600.0, 59.0, 20.0], "saved_object_attributes": {"_persistence": 0}, "style": "", "text": "live.object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-14", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [736.0, 520.0, 28.0, 20.0], "style": "", "text": "id 0"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [680.0, 496.0, 130.0, 20.0], "style": "", "text": "select 1 0"}}, {"box": {"color": [0.984314, 0.819608, 0.05098, 1.0], "fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [680.0, 600.0, 75.0, 20.0], "saved_object_attributes": {"_persistence": 0}, "style": "", "text": "live.observer"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-9", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [680.0, 575.0, 93.0, 20.0], "style": "", "text": "prepend property"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-7", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", "bang"], "patching_rect": [680.0, 520.0, 32.5, 20.0], "style": "", "text": "b"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [760.0, 552.0, 37.0, 20.0], "style": "", "text": "zl reg"}}, {"box": {"comment": "Property, observe, get, bang, set, anything", "id": "obj-51", "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [200.0, 24.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"comment": "Value (set)", "id": "obj-144", "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [488.0, 712.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"angle": 0.0, "bgcolor": [0.094118, 0.113725, 0.137255, 0.0], "border": 1, "id": "obj-46", "maxclass": "panel", "mode": 0, "numinlets": 1, "numoutlets": 0, "patching_rect": [24.0, 296.0, 208.0, 320.0], "proportion": 0.39, "rounded": 16, "style": ""}}], "lines": [{"patchline": {"destination": ["obj-12", 1], "disabled": 0, "hidden": 0, "source": ["obj-1", 1]}}, {"patchline": {"destination": ["obj-37", 0], "disabled": 0, "hidden": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-24", 0], "disabled": 0, "hidden": 0, "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-40", 0], "disabled": 0, "hidden": 0, "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-89", 1], "disabled": 0, "hidden": 0, "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-5", 0], "disabled": 0, "hidden": 0, "source": ["obj-108", 1]}}, {"patchline": {"destination": ["obj-62", 0], "disabled": 0, "hidden": 0, "source": ["obj-108", 0]}}, {"patchline": {"destination": ["obj-15", 0], "disabled": 0, "hidden": 0, "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-9", 0], "disabled": 0, "hidden": 0, "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-14", 0], "disabled": 0, "hidden": 0, "source": ["obj-13", 1]}}, {"patchline": {"destination": ["obj-7", 0], "disabled": 0, "hidden": 0, "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-10", 1], "disabled": 0, "hidden": 0, "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-43", 0], "disabled": 0, "hidden": 0, "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-22", 0], "disabled": 0, "hidden": 0, "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-24", 0], "disabled": 0, "hidden": 0, "source": ["obj-18", 1]}}, {"patchline": {"destination": ["obj-40", 0], "disabled": 0, "hidden": 0, "source": ["obj-18", 1]}}, {"patchline": {"destination": ["obj-43", 0], "disabled": 0, "hidden": 0, "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-10", 1], "disabled": 0, "hidden": 0, "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-18", 0], "disabled": 0, "hidden": 0, "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-84", 1], "disabled": 0, "hidden": 0, "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-64", 0], "disabled": 0, "hidden": 0, "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-6", 0], "disabled": 0, "hidden": 0, "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-144", 0], "disabled": 0, "hidden": 0, "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-33", 0], "disabled": 0, "hidden": 0, "source": ["obj-25", 1]}}, {"patchline": {"destination": ["obj-41", 0], "disabled": 0, "hidden": 0, "source": ["obj-25", 1]}}, {"patchline": {"destination": ["obj-85", 1], "disabled": 0, "hidden": 0, "source": ["obj-25", 1]}}, {"patchline": {"destination": ["obj-32", 0], "disabled": 0, "hidden": 0, "source": ["obj-28", 1]}}, {"patchline": {"destination": ["obj-58", 0], "disabled": 0, "hidden": 0, "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-42", 0], "disabled": 0, "hidden": 0, "source": ["obj-29", 1]}}, {"patchline": {"destination": ["obj-62", 0], "disabled": 0, "hidden": 0, "source": ["obj-29", 1]}}, {"patchline": {"destination": ["obj-62", 0], "disabled": 0, "hidden": 0, "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-11", 0], "disabled": 0, "hidden": 0, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-43", 0], "disabled": 0, "hidden": 0, "source": ["obj-3", 1]}}, {"patchline": {"destination": ["obj-37", 0], "disabled": 0, "hidden": 0, "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-17", 0], "disabled": 0, "hidden": 0, "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-44", 0], "disabled": 0, "hidden": 0, "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-45", 0], "disabled": 0, "hidden": 0, "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-2", 1], "disabled": 0, "hidden": 0, "source": ["obj-33", 2]}}, {"patchline": {"destination": ["obj-21", 1], "disabled": 0, "hidden": 0, "source": ["obj-33", 2]}}, {"patchline": {"destination": ["obj-37", 0], "disabled": 0, "hidden": 0, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-44", 1], "disabled": 0, "hidden": 0, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-59", 0], "disabled": 0, "hidden": 0, "midpoints": [658.25, 398.0, 312.875, 398.0, 312.875, 334.0, 337.5, 334.0], "source": ["obj-33", 1]}}, {"patchline": {"destination": ["obj-90", 0], "disabled": 0, "hidden": 0, "source": ["obj-36", 1]}}, {"patchline": {"destination": ["obj-13", 0], "disabled": 0, "hidden": 0, "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-73", 0], "disabled": 0, "hidden": 0, "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-76", 1], "disabled": 0, "hidden": 0, "source": ["obj-41", 2]}}, {"patchline": {"destination": ["obj-76", 0], "disabled": 0, "hidden": 0, "source": ["obj-41", 1]}}, {"patchline": {"destination": ["obj-33", 0], "disabled": 0, "hidden": 0, "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-108", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 5]}}, {"patchline": {"destination": ["obj-28", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 2]}}, {"patchline": {"destination": ["obj-30", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 6]}}, {"patchline": {"destination": ["obj-36", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 7]}}, {"patchline": {"destination": ["obj-47", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-49", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 3]}}, {"patchline": {"destination": ["obj-53", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 1]}}, {"patchline": {"destination": ["obj-74", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 4]}}, {"patchline": {"destination": ["obj-77", 1], "disabled": 0, "hidden": 0, "source": ["obj-43", 5]}}, {"patchline": {"destination": ["obj-90", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 8]}}, {"patchline": {"destination": ["obj-23", 0], "disabled": 0, "hidden": 0, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-56", 1], "disabled": 0, "hidden": 0, "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-44", 1], "disabled": 0, "hidden": 0, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-75", 0], "disabled": 0, "hidden": 0, "source": ["obj-49", 1]}}, {"patchline": {"destination": ["obj-25", 0], "disabled": 0, "hidden": 0, "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-43", 0], "disabled": 0, "hidden": 0, "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-23", 0], "disabled": 0, "hidden": 0, "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-23", 0], "disabled": 0, "hidden": 0, "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-23", 0], "disabled": 0, "hidden": 0, "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-56", 0], "disabled": 0, "hidden": 0, "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-8", 0], "disabled": 0, "hidden": 0, "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-41", 0], "disabled": 0, "hidden": 0, "source": ["obj-62", 0]}}, {"patchline": {"destination": ["obj-21", 0], "disabled": 0, "hidden": 0, "source": ["obj-64", 0]}}, {"patchline": {"destination": ["obj-66", 1], "disabled": 0, "hidden": 0, "source": ["obj-64", 0]}}, {"patchline": {"destination": ["obj-12", 0], "disabled": 0, "hidden": 0, "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-2", 0], "disabled": 0, "hidden": 0, "source": ["obj-7", 1]}}, {"patchline": {"destination": ["obj-29", 0], "disabled": 0, "hidden": 0, "source": ["obj-74", 0]}}, {"patchline": {"destination": ["obj-1", 0], "disabled": 0, "hidden": 0, "source": ["obj-75", 0]}}, {"patchline": {"destination": ["obj-31", 0], "disabled": 0, "hidden": 0, "source": ["obj-75", 0]}}, {"patchline": {"destination": ["obj-6", 1], "disabled": 0, "hidden": 0, "source": ["obj-75", 0]}}, {"patchline": {"destination": ["obj-97", 1], "disabled": 0, "hidden": 0, "source": ["obj-75", 0]}}, {"patchline": {"destination": ["obj-81", 0], "disabled": 0, "hidden": 0, "source": ["obj-76", 0]}}, {"patchline": {"destination": ["obj-64", 0], "disabled": 0, "hidden": 0, "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-10", 0], "disabled": 0, "hidden": 0, "source": ["obj-9", 0]}}, {"patchline": {"destination": ["obj-87", 1], "disabled": 0, "hidden": 0, "source": ["obj-9", 0]}}, {"patchline": {"destination": ["obj-17", 0], "disabled": 0, "hidden": 0, "source": ["obj-90", 0]}}, {"patchline": {"destination": ["obj-20", 1], "disabled": 0, "hidden": 0, "source": ["obj-90", 0]}}]}}