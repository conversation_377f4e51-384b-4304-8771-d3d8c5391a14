// clipQueueBridge.js
var sessionButtonPressed = false;
var queuedClips = 0;

function anything() {
    var a = arrayfromargs(arguments);
    
    // Check if this is a message from the session button UI
    if (a[0] === "Session") {
        sessionButtonPressed = (a[1] > 0);
        outlet(1, queuedClips);
        return;
    }
    
    // Check if this is a clear queue message
    if (a[0] === "Clear") {
        queuedClips = 0;
        outlet(1, queuedClips);
        // Send a clear message to the controller
        outlet(0, 144, 15, 127, 0);
        return;
    }
}

function list() {
    var a = arrayfromargs(arguments);
    
    // MIDI message format: [status, data1, data2]
    if (a.length >= 3) {
        var status = a[0];
        var data1 = a[1];
        var data2 = a[2];
        
        // Detect Session button (CC 51 on channel 1)
        if (status === 176 && data1 === 51) {
            sessionButtonPressed = (data2 > 0);
            outlet(1, queuedClips);
        }
        
        // Detect pad press when Session is held
        // Note On messages (144-159) with velocity > 0
        if (sessionButtonPressed && status >= 144 && status <= 159 && data2 > 0) {
            // Calculate track and scene from note number
            var track = data1 % 8;
            var scene = Math.floor(data1 / 8);
            
            // Create custom MIDI message for ClipQueueController
            // Channel 16 (15 in zero-index), note value encodes track and scene
            outlet(0, 144, 15, (scene << 4) | track, 127);
            
            // Increment queued clips counter
            queuedClips++;
            outlet(1, queuedClips);
        }
        
        // Pass through all other MIDI
        outlet(0, status, data1, data2);
    }
}