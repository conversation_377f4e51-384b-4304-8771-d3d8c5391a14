# Test Button Troubleshooting Guide

## Test Buttons Added (Now in Presentation View)
1. **"test_queuing 0 0"** - Position [10.0, 120.0] - ID: obj-300
2. **"inspect_clip 0 0"** - Position [140.0, 120.0] - ID: obj-301  
3. **"clear"** - Position [270.0, 120.0] - ID: obj-302

## Accessing Test Buttons
The test buttons are now visible in presentation mode:
1. Open the Max patch (clipQueue.maxpat)
2. Make sure you're in presentation mode (not edit mode)
3. Look for "Debug & Test Functions:" section
4. You should see three buttons below the label

## Troubleshooting Steps

### Step 1: Check if buttons are visible in presentation mode
- The buttons should be visible in the main patch interface
- Look for "Debug & Test Functions:" label
- Buttons should be arranged horizontally below it

### Step 2: Test basic JS communication
- Click the "clear" button first - this should work and show in Max console:
  ```
  Clear command received - clearing all clips and state
  ```

### Step 3: Test the debug buttons
- Click "inspect_clip 0 0" - should show:
  ```
  *** INSPECT_CLIP MESSAGE RECEIVED ***
  ```
- Click "test_queuing 0 0" - should show:
  ```
  *** TEST_QUEUING MESSAGE RECEIVED ***
  ```

### Step 4: If buttons aren't visible
1. Try toggling presentation mode (press 'p' or go to View → Presentation Mode)
2. Check if the patch window is large enough
3. Make sure you have the latest version of the patch

### Expected Debug Output for test_queuing (Updated)
```
*** TEST_QUEUING MESSAGE RECEIVED ***
Calling test_clip_queuing with args: 0 0
=== COMPREHENSIVE CLIP QUEUING TEST ===
Testing track 0 clip slot 0
Session tempo: [120]
Session is_playing: [0]
Testing ClipSlot path: live_set tracks 0 clip_slots 0
ClipSlot LiveAPI created successfully, ID: [some_number]
ClipSlot has_clip: [1] or [0]
Initial is_triggered: [0]
Attempting to call fire() on ClipSlot...
fire() call completed without error
is_triggered after calling fire(): [1] or [0]
...
```

### Expected Debug Output for inspect_clip (Updated)
```
*** INSPECT_CLIP MESSAGE RECEIVED ***
Calling inspect_clip_state with args: 0 0
=== CLIP STATE INSPECTION ===
Inspecting track 0 clip slot 0
ClipSlot LiveAPI ID: [some_number]
has_clip: [1] or [0]
is_triggered: [0] or [1]
will_record_on_start: [0]
...
```

## Audio Blocking Issue Fixed
The JavaScript now properly ignores "signal" messages, so the patch should no longer block audio to master.

## API Changes Made
- Replaced `set_fire_button_state` with `fire()` method
- Removed all references to `fire_button_state` property
- Updated all test and inspection functions accordingly

## Connection Verification
The buttons should be connected to:
- obj-209 (the js clipLauncher.js object)
- Check patch connections in edit mode to verify if needed
