# LED Test Plan v7.5 - Live API Button_Matrix Control (FINAL)

## Status: TESTING - Back to Live API with Proper Setup

## Overview
**LED Control v7.5** returns to the Live API approach but implements it correctly based on insights from the Push2-ArrangeMode project. The key missing pieces were proper control_surfaces path setup and Button_Matrix control acquisition.

## Technical Implementation

### Max Patch Components:
1. **`route led_debug call`** (obj-209a) - Routes LED commands from JavaScript
2. **`prepend call send_value`** (obj-209g) - Prepends "call send_value" to coordinate data
3. **`live.object`** (obj-209f) - Main control object for Button_Matrix  
4. **`loadbang`** (obj-209k) - Triggers initialization
5. **`path control_surfaces 0`** (obj-209j) - Sets control surface path
6. **`call get_control_by_name Button_Matrix`** (obj-209l) - Gets Button_Matrix control

### JavaScript Functions:
- **`send_test_led_commands()`** - Sends `outlet(0, "call", "send_value", scene, track, velocity)`
- **`enter_session_mode()`** - Activates LED preservation system
- **`exit_session_mode()`** - Clears LEDs and restores normal behavior

### Data Flow:
```
JavaScript: outlet(0, "call", "send_value", scene, track, velocity)
    ↓
Max route: separates "call" commands
    ↓  
prepend: creates "call send_value scene track velocity"
    ↓
live.object: sends to Button_Matrix control (setup via loadbang)
    ↓
Push 2 Hardware: LED lights up based on velocity value
```

### Initialization Sequence:
```
loadbang triggers:
1. "path control_surfaces 0" → live.object
2. "call get_control_by_name Button_Matrix" → live.object

This properly configures the live.object to control Button_Matrix
```

## Key Differences from Previous Attempts

### v7.5 vs v7.4 (MIDI):
- **Abandoned direct MIDI** - noteout/midiout approach wasn't working
- **Proper Live API setup** - Uses loadbang to configure control_surfaces path
- **Button_Matrix acquisition** - Gets control by name instead of hardcoded path

### v7.5 vs v7.2 (Failed Live API):
- **Added loadbang initialization** - Previous version had no proper setup
- **Correct message format** - "call send_value" with individual parameters
- **Control acquisition** - Uses get_control_by_name instead of direct path reference

## Expected Behavior

### Startup (loadbang):
1. **Max console should show:** No "live.object: doesn't understand path" errors
2. **Control setup:** Button_Matrix control should be acquired successfully

### When Session Button Pressed:
1. **JavaScript logs:** "Entering Session mode", "Found clip at scene X track Y", "LED control: scene X track Y color Z"
2. **Max console:** No "no valid object set" errors
3. **Push 2 LEDs:** Test area (top-left 2x2) should light up:
   - **Red (127):** Playing clips
   - **Green (126):** Stopped clips  
   - **Off (0):** Empty slots

### When Session Button Released:
1. **JavaScript logs:** "Exiting Session mode", "Cleared test area LEDs"
2. **Push 2 LEDs:** Test area should turn off

## Troubleshooting

### If Still No LEDs:
1. **Check control_surfaces index:** Try changing "path control_surfaces 0" to "path control_surfaces 1"
2. **Verify Button_Matrix control:** Max console should show successful control acquisition
3. **Test with manual commands:** Send manual "call send_value 0 0 127" to test setup

### Common Issues:
- **"no valid object set":** loadbang initialization failed
- **"doesn't understand send_value":** Button_Matrix control not acquired
- **Wrong control_surfaces index:** Push 2 might be on index 1 instead of 0

## Success Criteria
- [ ] No Live API error messages on startup
- [ ] Button_Matrix control acquired successfully
- [ ] LEDs light up in test area when Session mode activated
- [ ] LEDs turn off when Session mode exits
- [ ] Different colors for playing vs stopped clips

## Implementation Notes
- **Based on working Push2-ArrangeMode project** - Uses identical Live API approach
- **Proper initialization sequence** - loadbang ensures control_surfaces setup
- **Individual parameters** - send_value gets scene, track, velocity as separate values
- **Backwards compatible** - Core Session + Play functionality unchanged

This is our final attempt using the proven working method from Push2-ArrangeMode project.
