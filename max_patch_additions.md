# Max Patch Additions for LED Control

## Required Changes to clipQueue.maxpat

To enable LED control debugging and eventually full LED preservation, add these objects to the Max patch:

### 1. LED Debug Route (add near existing route objects)
- **Object:** `route led_debug`
- **Purpose:** Capture LED debug messages from JavaScript
- **Connection:** Connect from JavaScript outlet to this route object
- **Output:** Connect to a `print` object to see debug messages in Max console

### 2. LED Control Routing (future enhancement)
- **Object:** `route set_pad_led test_led`  
- **Purpose:** Route LED control commands to appropriate handlers
- **Inputs:** 
  - `set_pad_led scene track velocity` - for full LED matrix control
  - `test_led pad_note velocity` - for single LED testing

### 3. MIDI Output for Push 2 LEDs (when ready)
- **Object:** `midiout` or `noteout` 
- **Purpose:** Send MIDI note messages to Push 2 for LED control
- **Configuration:** Set to Push 2 MIDI output device
- **Message Format:** Note On (144), note number (36-99), velocity (0-127)

## Implementation Steps:

1. **Phase 1 (Current):** Add `route led_debug` + `print` to see JavaScript LED messages
2. **Phase 2:** Add MIDI output routing for actual LED control  
3. **Phase 3:** Connect LED commands to Push 2 MIDI output

## Testing:

After adding the debug route, holding Session button should show messages like:
```
led_debug session_enter
led_debug clip_found 0 0 36
led_debug clip_found 0 1 37
led_debug session_exit
```

This confirms JavaScript → Max communication is working before implementing actual LED control.
