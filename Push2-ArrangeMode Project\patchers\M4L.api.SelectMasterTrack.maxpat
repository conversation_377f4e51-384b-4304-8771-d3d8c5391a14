{"patcher": {"fileversion": 1, "rect": [112.0, 66.0, 528.0, 416.0], "bglocked": 0, "defrect": [112.0, 66.0, 528.0, 416.0], "openrect": [0.0, 0.0, 0.0, 0.0], "openinpresentation": 0, "default_fontsize": 10.0, "default_fontface": 0, "default_fontname": "Arial Bold", "gridonopen": 0, "gridsize": [8.0, 8.0], "gridsnaponopen": 0, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "boxes": [{"box": {"maxclass": "comment", "text": "--->", "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "patching_rect": [120.0, 208.0, 27.0, 18.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-23"}}, {"box": {"maxclass": "comment", "text": "we need the id of the Master Track - so we set a live.path object to point at it - it will output it", "linecount": 5, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "patching_rect": [16.0, 208.0, 108.0, 64.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-53"}}, {"box": {"maxclass": "comment", "text": "< this live.object points to the current Live Set's main view - now we set the property \"selected_track\" to the given id", "linecount": 3, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "patching_rect": [312.0, 296.0, 187.0, 41.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-17"}}, {"box": {"maxclass": "message", "text": "help live.object", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [312.0, 336.0, 83.0, 16.0], "bgcolor": [0.984314, 0.819608, 0.05098, 1.0], "id": "obj-18"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pcontrol", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [312.0, 360.0, 50.0, 18.0], "hidden": 1, "id": "obj-20"}}, {"box": {"maxclass": "comment", "text": "< first we set the live.path to the main view of the current Live Set and feed the live.object below with its ID", "linecount": 4, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "patching_rect": [312.0, 152.0, 158.0, 52.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-26"}}, {"box": {"maxclass": "message", "text": "help live.path", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [312.0, 200.0, 74.0, 16.0], "bgcolor": [0.984314, 0.819608, 0.05098, 1.0], "id": "obj-52"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pcontrol", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [312.0, 224.0, 50.0, 18.0], "hidden": 1, "id": "obj-51"}}, {"box": {"maxclass": "comment", "prototypename": "ML.subpatcher-title", "text": "Select Master Track", "fontname": "Arial Bold Italic", "numinlets": 1, "numoutlets": 0, "fontsize": 24.0, "patching_rect": [16.0, 16.0, 240.0, 34.0], "textcolor": [0.3, 0.34, 0.4, 1.0], "frgb": [0.3, 0.34, 0.4, 1.0], "id": "obj-48"}}, {"box": {"maxclass": "comment", "prototypename": "<PERSON><PERSON><PERSON>patcher-story", "text": "Select the Master Track of the current Live set.", "fontname": "Arial Italic", "numinlets": 1, "numoutlets": 0, "fontsize": 11.0, "patching_rect": [16.0, 48.0, 239.0, 19.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-50"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "fontname": "Arial Bold", "numinlets": 2, "color": [0.984314, 0.819608, 0.05098, 1.0], "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [240.0, 296.0, 60.0, 18.0], "id": "obj-10"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0, "outlettype": ["bang", ""], "patching_rect": [264.0, 176.0, 36.0, 18.0], "id": "obj-9"}}, {"box": {"maxclass": "message", "text": "path live_set view", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [248.0, 128.0, 96.0, 16.0], "id": "obj-8"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "fontname": "Arial Bold", "numinlets": 1, "color": [0.984314, 0.819608, 0.05098, 1.0], "numoutlets": 3, "fontsize": 10.0, "outlettype": ["", "", ""], "patching_rect": [248.0, 152.0, 50.0, 18.0], "id": "obj-7"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend set selected_track", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [144.0, 264.0, 139.0, 18.0], "id": "obj-6"}}, {"box": {"maxclass": "message", "text": "path live_set master_track", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [152.0, 208.0, 136.0, 16.0], "id": "obj-4"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "fontname": "Arial Bold", "numinlets": 1, "color": [0.984314, 0.819608, 0.05098, 1.0], "numoutlets": 3, "fontsize": 10.0, "outlettype": ["", "", ""], "patching_rect": [152.0, 232.0, 51.0, 18.0], "id": "obj-2"}}, {"box": {"maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [248.0, 96.0, 18.0, 18.0], "id": "obj-1", "comment": ""}}], "lines": [{"patchline": {"source": ["obj-2", 1], "destination": ["obj-6", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-6", 0], "destination": ["obj-10", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-18", 0], "destination": ["obj-20", 0], "hidden": 1, "midpoints": []}}, {"patchline": {"source": ["obj-52", 0], "destination": ["obj-51", 0], "hidden": 1, "midpoints": []}}, {"patchline": {"source": ["obj-9", 1], "destination": ["obj-10", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-1", 0], "destination": ["obj-8", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-2", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-7", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-7", 1], "destination": ["obj-9", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-9", 0], "destination": ["obj-4", 0], "hidden": 0, "midpoints": []}}]}}