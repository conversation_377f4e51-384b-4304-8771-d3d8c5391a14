{"patcher": {"fileversion": 1, "rect": [50.0, 94.0, 826.0, 514.0], "bglocked": 0, "defrect": [50.0, 94.0, 826.0, 514.0], "openrect": [0.0, 0.0, 0.0, 0.0], "openinpresentation": 0, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 0, "gridsize": [15.0, 15.0], "gridsnaponopen": 0, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "boxes": [{"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route master_track", "numoutlets": 2, "patching_rect": [181.0, 360.0, 101.0, 18.0], "id": "obj-29", "outlettype": ["", ""], "fontname": "Arial Bold", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "outlet", "hint": "master_track id", "annotation": "master_track id", "numoutlets": 0, "patching_rect": [189.0, 458.0, 25.0, 25.0], "id": "obj-31", "numinlets": 1, "comment": "master_track id"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl len", "numoutlets": 2, "patching_rect": [300.0, 408.0, 37.0, 18.0], "id": "obj-11", "outlettype": ["", ""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl delace", "numoutlets": 2, "patching_rect": [300.0, 384.0, 53.0, 18.0], "id": "obj-13", "outlettype": ["", ""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route return_tracks", "numoutlets": 2, "patching_rect": [300.0, 360.0, 102.0, 18.0], "id": "obj-18", "outlettype": ["", ""], "fontname": "Arial Bold", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "outlet", "hint": "Number of return_tracks", "annotation": "Number of return_tracks", "numoutlets": 0, "patching_rect": [300.0, 433.0, 25.0, 25.0], "id": "obj-25", "numinlets": 1, "comment": "Number of return_tracks"}}, {"box": {"maxclass": "outlet", "hint": "return_tracks id", "annotation": "return_tracks id", "numoutlets": 0, "patching_rect": [354.0, 433.0, 25.0, 25.0], "id": "obj-26", "numinlets": 1, "comment": "return_tracks id"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "patching_rect": [189.0, 330.0, 69.0, 18.0], "id": "obj-9", "outlettype": [""], "fontname": "Arial Bold", "color": [0.984314, 0.819608, 0.05098, 1.0], "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "patching_rect": [309.0, 330.0, 67.0, 18.0], "id": "obj-7", "outlettype": [""], "fontname": "Arial Bold", "color": [0.984314, 0.819608, 0.05098, 1.0], "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "get tracks", "numoutlets": 1, "patching_rect": [416.0, 295.0, 58.0, 16.0], "id": "obj-2", "outlettype": [""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "get return_tracks", "numoutlets": 1, "patching_rect": [270.0, 300.0, 92.0, 16.0], "id": "obj-1", "outlettype": [""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "-->", "numoutlets": 0, "patching_rect": [147.0, 386.0, 23.0, 18.0], "id": "obj-15", "fontname": "<PERSON><PERSON>", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "the list has the format \"id 1 id 2 id 3\" - so we get rid of the \"id\" tokens and count the members", "linecount": 5, "numoutlets": 0, "patching_rect": [43.0, 386.0, 107.0, 64.0], "id": "obj-14", "fontname": "<PERSON><PERSON>", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "< after the live.object points to the current Live Set we ask it for the list of tracks", "linecount": 2, "numoutlets": 0, "patching_rect": [600.0, 405.0, 208.0, 29.0], "id": "obj-10", "fontname": "<PERSON><PERSON>", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "help live.object", "numoutlets": 1, "patching_rect": [561.0, 437.0, 83.0, 16.0], "id": "obj-12", "bgcolor": [0.984314, 0.819608, 0.05098, 1.0], "outlettype": [""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pcontrol", "numoutlets": 1, "patching_rect": [561.0, 461.0, 50.0, 18.0], "id": "obj-16", "hidden": 1, "outlettype": [""], "fontname": "Arial Bold", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "< first we set the live.path to the current Live Set and feed the live.object below with its ID", "linecount": 2, "numoutlets": 0, "patching_rect": [561.0, 220.0, 214.0, 29.0], "id": "obj-53", "fontname": "<PERSON><PERSON>", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "help live.path", "numoutlets": 1, "patching_rect": [561.0, 252.0, 74.0, 16.0], "id": "obj-52", "bgcolor": [0.984314, 0.819608, 0.05098, 1.0], "outlettype": [""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pcontrol", "numoutlets": 1, "patching_rect": [561.0, 276.0, 50.0, 18.0], "id": "obj-51", "hidden": 1, "outlettype": [""], "fontname": "Arial Bold", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "prototypename": "ML.subpatcher-title", "text": "Get All Track Count And Their Id", "linecount": 2, "numoutlets": 0, "patching_rect": [50.0, 25.0, 292.0, 62.0], "id": "obj-48", "frgb": [0.3, 0.34, 0.4, 1.0], "textcolor": [0.3, 0.34, 0.4, 1.0], "fontname": "Arial Bold Italic", "numinlets": 1, "fontsize": 24.0}}, {"box": {"maxclass": "comment", "prototypename": "<PERSON><PERSON><PERSON>patcher-story", "text": "Get the number of tracks in the current Live set, plus their Id.", "linecount": 2, "numoutlets": 0, "patching_rect": [50.0, 87.0, 294.0, 32.0], "id": "obj-50", "fontname": "Arial Italic", "numinlets": 1, "fontsize": 11.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l b l b l", "numoutlets": 6, "patching_rect": [180.0, 255.0, 313.5, 18.0], "id": "obj-17", "outlettype": ["bang", "", "bang", "", "bang", ""], "fontname": "Arial Bold", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl len", "numoutlets": 2, "patching_rect": [420.0, 408.0, 37.0, 18.0], "id": "obj-8", "outlettype": ["", ""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl delace", "numoutlets": 2, "patching_rect": [420.0, 384.0, 53.0, 18.0], "id": "obj-19", "outlettype": ["", ""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route tracks", "numoutlets": 2, "patching_rect": [420.0, 360.0, 68.0, 18.0], "id": "obj-20", "outlettype": ["", ""], "fontname": "Arial Bold", "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "get master_track", "numoutlets": 1, "patching_rect": [155.0, 300.0, 91.0, 16.0], "id": "obj-21", "outlettype": [""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "path live_set", "numoutlets": 1, "patching_rect": [202.0, 196.0, 71.0, 16.0], "id": "obj-22", "outlettype": [""], "fontname": "Arial Bold", "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "numoutlets": 3, "patching_rect": [202.0, 220.0, 67.0, 18.0], "id": "obj-23", "outlettype": ["", "", ""], "fontname": "Arial Bold", "color": [0.984314, 0.819608, 0.05098, 1.0], "numinlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "patching_rect": [416.0, 326.0, 78.0, 18.0], "id": "obj-24", "outlettype": [""], "fontname": "Arial Bold", "color": [0.984314, 0.819608, 0.05098, 1.0], "numinlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "inlet", "numoutlets": 1, "patching_rect": [195.0, 150.0, 25.0, 25.0], "id": "obj-32", "outlettype": ["bang"], "numinlets": 0, "comment": ""}}, {"box": {"maxclass": "outlet", "hint": "Number of tracks", "annotation": "Number of tracks", "numoutlets": 0, "patching_rect": [420.0, 480.0, 25.0, 25.0], "id": "obj-33", "numinlets": 1, "comment": "Number of tracks"}}, {"box": {"maxclass": "outlet", "hint": "Tracks id", "annotation": "Tracks id", "numoutlets": 0, "patching_rect": [465.0, 480.0, 25.0, 25.0], "id": "obj-34", "numinlets": 1, "comment": "Tracks id"}}], "lines": [{"patchline": {"source": ["obj-17", 4], "destination": ["obj-2", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-17", 5], "destination": ["obj-24", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-17", 0], "destination": ["obj-21", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-17", 1], "destination": ["obj-9", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-33", 0], "hidden": 0, "midpoints": [429.5, 469.5, 429.5, 469.5]}}, {"patchline": {"source": ["obj-32", 0], "destination": ["obj-22", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-22", 0], "destination": ["obj-23", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-19", 1], "destination": ["obj-8", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-52", 0], "destination": ["obj-51", 0], "hidden": 1, "midpoints": []}}, {"patchline": {"source": ["obj-12", 0], "destination": ["obj-16", 0], "hidden": 1, "midpoints": []}}, {"patchline": {"source": ["obj-2", 0], "destination": ["obj-24", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-20", 0], "destination": ["obj-34", 0], "hidden": 0, "midpoints": [429.5, 380.0, 474.5, 380.0]}}, {"patchline": {"source": ["obj-20", 0], "destination": ["obj-19", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-24", 0], "destination": ["obj-20", 0], "hidden": 0, "midpoints": [425.5, 350.0, 429.5, 350.0]}}, {"patchline": {"source": ["obj-23", 1], "destination": ["obj-17", 0], "hidden": 0, "midpoints": [235.5, 246.0, 189.5, 246.0]}}, {"patchline": {"source": ["obj-17", 2], "destination": ["obj-1", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-17", 3], "destination": ["obj-7", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-21", 0], "destination": ["obj-9", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-1", 0], "destination": ["obj-7", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-13", 1], "destination": ["obj-11", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-11", 0], "destination": ["obj-25", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-18", 0], "destination": ["obj-26", 0], "hidden": 0, "midpoints": [309.5, 380.0, 363.5, 380.0]}}, {"patchline": {"source": ["obj-18", 0], "destination": ["obj-13", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-7", 0], "destination": ["obj-18", 0], "hidden": 0, "midpoints": [318.5, 355.0, 309.5, 355.0]}}, {"patchline": {"source": ["obj-29", 0], "destination": ["obj-31", 0], "hidden": 0, "midpoints": [190.5, 421.0, 198.5, 421.0]}}, {"patchline": {"source": ["obj-9", 0], "destination": ["obj-29", 0], "hidden": 0, "midpoints": []}}]}}