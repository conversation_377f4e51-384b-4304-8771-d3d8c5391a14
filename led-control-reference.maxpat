{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [100.0, 100.0, 800.0, 600.0], "bglocked": 0, "openinpresentation": 1, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "presentation_rect": [100.0, 100.0, 400.0, 300.0], "boxes": [{"box": {"maxclass": "comment", "text": "🎯 Push 2 LED Control - Working Reference", "presentation_rect": [10.0, 10.0, 400.0, 25.0], "presentation": 1, "patching_rect": [30.0, 30.0, 400.0, 25.0], "numinlets": 1, "numoutlets": 0, "fontsize": 16.0, "fontface": 1, "id": "obj-title"}}, {"box": {"maxclass": "comment", "text": "This is the COMPLETE working LED control system to integrate into clipQueue.maxpat", "presentation_rect": [10.0, 40.0, 500.0, 20.0], "presentation": 1, "patching_rect": [30.0, 60.0, 500.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 12.0, "id": "obj-desc"}}, {"box": {"maxclass": "comment", "text": "=== STEP 1: INITIALIZE PUSH 2 CONNECTION ===", "patching_rect": [30.0, 100.0, 300.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-step1"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadbang", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [30.0, 130.0, 58.0, 22.0], "numinlets": 1, "id": "obj-loadbang"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b b b b b", "numoutlets": 5, "outlettype": ["bang", "bang", "bang", "bang", "bang"], "patching_rect": [30.0, 160.0, 70.0, 22.0], "numinlets": 1, "id": "obj-trigger"}}, {"box": {"maxclass": "message", "text": "path live_set", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 190.0, 75.0, 22.0], "numinlets": 2, "id": "obj-path-msg"}}, {"box": {"maxclass": "message", "text": "path control_surfaces 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [120.0, 190.0, 127.0, 22.0], "numinlets": 2, "id": "obj-push-path-msg"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "saved_object_attributes": {"_persistence": 1, "parameter_enable": 1}, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [30.0, 220.0, 67.0, 22.0], "id": "obj-live-path", "color": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s ---<PERSON><PERSON><PERSON><PERSON>", "numoutlets": 0, "patching_rect": [30.0, 250.0, 85.0, 22.0], "numinlets": 1, "id": "obj-send-path"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r ---<PERSON><PERSON><PERSON><PERSON>", "numoutlets": 1, "outlettype": [""], "patching_rect": [350.0, 220.0, 85.0, 22.0], "numinlets": 0, "id": "obj-receive-path"}}, {"box": {"maxclass": "comment", "text": "=== STEP 2: GET AND GRAB BUTTON MATRIX ===", "patching_rect": [200.0, 100.0, 300.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-step2"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "delay 100", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [50.0, 190.0, 58.0, 22.0], "numinlets": 2, "id": "obj-delay1"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [50.0, 220.0, 200.0, 22.0], "numinlets": 2, "id": "obj-get-control"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "delay 200", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [70.0, 190.0, 58.0, 22.0], "numinlets": 2, "id": "obj-delay2"}}, {"box": {"maxclass": "message", "text": "call grab_control Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [70.0, 220.0, 160.0, 22.0], "numinlets": 2, "id": "obj-grab-control"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r ---LEDControl", "numoutlets": 1, "outlettype": [""], "patching_rect": [500.0, 220.0, 85.0, 22.0], "numinlets": 0, "id": "obj-receive-led"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t l l", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [500.0, 250.0, 30.0, 22.0], "numinlets": 1, "id": "obj-led-split"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend call send_midi 144", "numoutlets": 1, "outlettype": [""], "patching_rect": [500.0, 250.0, 150.0, 22.0], "numinlets": 1, "id": "obj-prepend-led"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "gate 1 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [500.0, 280.0, 50.0, 22.0], "numinlets": 2, "id": "obj-led-gate"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [350.0, 310.0, 62.0, 22.0], "numinlets": 2, "id": "obj-live-object", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "delay 800", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [90.0, 190.0, 58.0, 22.0], "numinlets": 2, "id": "obj-delay3"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "delay 200", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [90.0, 220.0, 58.0, 22.0], "numinlets": 2, "id": "obj-delay-enable"}}, {"box": {"maxclass": "message", "text": "1", "numoutlets": 1, "outlettype": [""], "patching_rect": [90.0, 250.0, 29.5, 22.0], "numinlets": 2, "id": "obj-enable-led"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print push2_status", "numoutlets": 0, "patching_rect": [350.0, 340.0, 100.0, 22.0], "numinlets": 1, "id": "obj-print-status"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print path_set", "numoutlets": 0, "patching_rect": [450.0, 250.0, 75.0, 22.0], "numinlets": 1, "id": "obj-print-path"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print led_command", "numoutlets": 0, "patching_rect": [550.0, 280.0, 100.0, 22.0], "numinlets": 1, "id": "obj-print-led"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [540.0, 340.0, 62.0, 22.0], "numinlets": 2, "id": "obj-live-object-direct", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "saved_object_attributes": {"_persistence": 1, "parameter_enable": 1}, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [620.0, 310.0, 67.0, 22.0], "id": "obj-direct-live-path", "color": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "message", "text": "path control_surfaces 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [620.0, 280.0, 127.0, 22.0], "numinlets": 2, "id": "obj-direct-path"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadbang", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [620.0, 250.0, 58.0, 22.0], "numinlets": 1, "id": "obj-direct-loadbang"}}, {"box": {"maxclass": "button", "patching_rect": [700.0, 250.0, 24.0, 24.0], "presentation_rect": [300.0, 100.0, 60.0, 30.0], "presentation": 1, "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "id": "obj-simple-test"}}, {"box": {"maxclass": "message", "text": "144 36 127", "numoutlets": 1, "outlettype": [""], "patching_rect": [700.0, 280.0, 70.0, 22.0], "numinlets": 2, "id": "obj-simple-midi"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "midiformat", "numoutlets": 1, "outlettype": ["int"], "patching_rect": [700.0, 310.0, 65.0, 22.0], "numinlets": 7, "id": "obj-simple-midiformat"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "midievent", "numoutlets": 0, "patching_rect": [700.0, 340.0, 65.0, 22.0], "numinlets": 2, "id": "obj-simple-midievent"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "unpack 0 0", "numoutlets": 2, "outlettype": ["int", "int"], "patching_rect": [540.0, 280.0, 60.0, 22.0], "numinlets": 1, "id": "obj-unpack-led"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print direct_midi", "numoutlets": 0, "patching_rect": [540.0, 250.0, 85.0, 22.0], "numinlets": 1, "id": "obj-print-direct"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [540.0, 370.0, 62.0, 22.0], "numinlets": 2, "id": "obj-track-object", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "saved_object_attributes": {"_persistence": 1, "parameter_enable": 1}, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [540.0, 340.0, 67.0, 22.0], "id": "obj-track-path", "color": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "message", "text": "path live_set tracks 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [540.0, 310.0, 120.0, 22.0], "numinlets": 2, "id": "obj-track-path-msg"}}, {"box": {"maxclass": "comment", "text": "=== STEP 3: LED CONTROL SYSTEM ===", "patching_rect": [30.0, 320.0, 300.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-step3"}}, {"box": {"maxclass": "comment", "text": "LED Test Buttons (click to test):", "presentation_rect": [20.0, 70.0, 200.0, 20.0], "presentation": 1, "patching_rect": [30.0, 350.0, 200.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-test-label"}}, {"box": {"maxclass": "button", "patching_rect": [30.0, 380.0, 24.0, 24.0], "presentation_rect": [30.0, 100.0, 60.0, 30.0], "presentation": 1, "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "id": "obj-test1"}}, {"box": {"maxclass": "message", "text": "36 127", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 410.0, 50.0, 22.0], "numinlets": 2, "id": "obj-led1-msg"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s ---LEDControl", "numoutlets": 0, "patching_rect": [30.0, 440.0, 85.0, 22.0], "numinlets": 1, "id": "obj-send-led1"}}, {"box": {"maxclass": "comment", "text": "BRIGHT (127)", "presentation_rect": [20.0, 135.0, 80.0, 20.0], "presentation": 1, "patching_rect": [10.0, 380.0, 80.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "id": "obj-label1"}}, {"box": {"maxclass": "button", "patching_rect": [200.0, 380.0, 24.0, 24.0], "presentation_rect": [120.0, 100.0, 60.0, 30.0], "presentation": 1, "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "id": "obj-test2"}}, {"box": {"maxclass": "message", "text": "37 17", "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 410.0, 40.0, 22.0], "numinlets": 2, "id": "obj-led2-msg"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s ---LEDControl", "numoutlets": 0, "patching_rect": [200.0, 440.0, 85.0, 22.0], "numinlets": 1, "id": "obj-send-led2"}}, {"box": {"maxclass": "comment", "text": "GREEN (126)", "presentation_rect": [110.0, 135.0, 90.0, 20.0], "presentation": 1, "patching_rect": [180.0, 380.0, 90.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "id": "obj-label2"}}, {"box": {"maxclass": "button", "patching_rect": [370.0, 380.0, 24.0, 24.0], "presentation_rect": [210.0, 100.0, 60.0, 30.0], "presentation": 1, "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "id": "obj-test3"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b b", "numoutlets": 2, "outlettype": ["bang", "bang"], "patching_rect": [370.0, 405.0, 35.0, 22.0], "numinlets": 1, "id": "obj-trigger-off"}}, {"box": {"maxclass": "message", "text": "36 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [370.0, 430.0, 40.0, 22.0], "numinlets": 2, "id": "obj-led3-msg"}}, {"box": {"maxclass": "message", "text": "36 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [420.0, 430.0, 40.0, 22.0], "numinlets": 2, "id": "obj-led4-msg"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s ---LEDControl", "numoutlets": 0, "patching_rect": [370.0, 460.0, 85.0, 22.0], "numinlets": 1, "id": "obj-send-led3"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s ---LEDControl", "numoutlets": 0, "patching_rect": [420.0, 460.0, 85.0, 22.0], "numinlets": 1, "id": "obj-send-led4"}}, {"box": {"maxclass": "comment", "text": "TURN ALL OFF", "presentation_rect": [200.0, 135.0, 90.0, 20.0], "presentation": 1, "patching_rect": [350.0, 380.0, 60.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "id": "obj-label3"}}, {"box": {"maxclass": "comment", "text": "SUCCESS NOTES:", "presentation_rect": [20.0, 180.0, 120.0, 20.0], "presentation": 1, "patching_rect": [30.0, 480.0, 120.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-notes-title"}}, {"box": {"maxclass": "comment", "text": "• LED commands: send light values to s ---LEDControl\n• System: r ---LEDControl → prepend call set_light → gate → live.object\n• Uses Push2ArrangeMode method: call set_light [value]\n• Value 127 = bright, 126 = green, 4 = dim, 0 = off\n• Must grab Button_Matrix control first!\n• Gate prevents LED commands until Push 2 is ready", "presentation_rect": [20.0, 200.0, 350.0, 100.0], "presentation": 1, "patching_rect": [30.0, 500.0, 450.0, 120.0], "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "id": "obj-notes"}}], "lines": [{"patchline": {"source": ["obj-loadbang", 0], "destination": ["obj-trigger", 0]}}, {"patchline": {"source": ["obj-trigger", 1], "destination": ["obj-delay1", 0]}}, {"patchline": {"source": ["obj-trigger", 2], "destination": ["obj-delay2", 0]}}, {"patchline": {"source": ["obj-trigger", 3], "destination": ["obj-delay3", 0]}}, {"patchline": {"source": ["obj-trigger", 4], "destination": ["obj-push-path-msg", 0]}}, {"patchline": {"source": ["obj-push-path-msg", 0], "destination": ["obj-live-path", 0]}}, {"patchline": {"source": ["obj-live-path", 0], "destination": ["obj-send-path", 0]}}, {"patchline": {"source": ["obj-delay1", 0], "destination": ["obj-get-control", 0]}}, {"patchline": {"source": ["obj-delay2", 0], "destination": ["obj-grab-control", 0]}}, {"patchline": {"source": ["obj-delay3", 0], "destination": ["obj-delay-enable", 0]}}, {"patchline": {"source": ["obj-delay-enable", 0], "destination": ["obj-enable-led", 0]}}, {"patchline": {"source": ["obj-receive-path", 0], "destination": ["obj-live-object", 0]}}, {"patchline": {"source": ["obj-receive-path", 0], "destination": ["obj-print-path", 0]}}, {"patchline": {"source": ["obj-get-control", 0], "destination": ["obj-live-object", 1]}}, {"patchline": {"source": ["obj-grab-control", 0], "destination": ["obj-live-object", 1]}}, {"patchline": {"source": ["obj-receive-led", 0], "destination": ["obj-led-split", 0]}}, {"patchline": {"source": ["obj-led-split", 0], "destination": ["obj-prepend-led", 0]}}, {"patchline": {"source": ["obj-track-path-msg", 0], "destination": ["obj-track-path", 0]}}, {"patchline": {"source": ["obj-track-path", 0], "destination": ["obj-track-object", 0]}}, {"patchline": {"source": ["obj-prepend-led", 0], "destination": ["obj-led-gate", 1]}}, {"patchline": {"source": ["obj-enable-led", 0], "destination": ["obj-led-gate", 0]}}, {"patchline": {"source": ["obj-led-gate", 0], "destination": ["obj-live-object", 0]}}, {"patchline": {"source": ["obj-led-gate", 0], "destination": ["obj-print-led", 0]}}, {"patchline": {"source": ["obj-live-object", 0], "destination": ["obj-print-status", 0]}}, {"patchline": {"source": ["obj-test1", 0], "destination": ["obj-led1-msg", 0]}}, {"patchline": {"source": ["obj-led1-msg", 0], "destination": ["obj-send-led1", 0]}}, {"patchline": {"source": ["obj-test2", 0], "destination": ["obj-led2-msg", 0]}}, {"patchline": {"source": ["obj-led2-msg", 0], "destination": ["obj-send-led2", 0]}}, {"patchline": {"source": ["obj-test3", 0], "destination": ["obj-trigger-off", 0]}}, {"patchline": {"source": ["obj-trigger-off", 0], "destination": ["obj-led3-msg", 0]}}, {"patchline": {"source": ["obj-trigger-off", 1], "destination": ["obj-led4-msg", 0]}}, {"patchline": {"source": ["obj-led3-msg", 0], "destination": ["obj-send-led3", 0]}}, {"patchline": {"source": ["obj-led4-msg", 0], "destination": ["obj-send-led4", 0]}}, {"patchline": {"source": ["obj-direct-prepend", 0], "destination": ["obj-live-object-direct", 1]}}, {"patchline": {"source": ["obj-direct-loadbang", 0], "destination": ["obj-direct-path", 0]}}, {"patchline": {"source": ["obj-direct-path", 0], "destination": ["obj-direct-live-path", 0]}}, {"patchline": {"source": ["obj-direct-live-path", 0], "destination": ["obj-live-object-direct", 0]}}, {"patchline": {"source": ["obj-simple-test", 0], "destination": ["obj-simple-midi", 0]}}, {"patchline": {"source": ["obj-simple-midi", 0], "destination": ["obj-simple-midiformat", 0]}}, {"patchline": {"source": ["obj-simple-midiformat", 0], "destination": ["obj-simple-midievent", 0]}}], "dependency_cache": [], "autosave": 0}}