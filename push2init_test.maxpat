{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 87.0, 640.0, 480.0], "bglocked": 0, "openinpresentation": 0, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"maxclass": "comment", "text": "Push2 Session Button Observer", "numoutlets": 0, "patching_rect": [50.0, 40.0, 200.0, 20.0], "numinlets": 1, "id": "obj-12", "fontname": "Arial Bold", "fontsize": 12.0}}, {"box": {"maxclass": "comment", "text": "127 = pressed, 0 = released", "numoutlets": 0, "patching_rect": [200.0, 400.0, 150.0, 20.0], "numinlets": 1, "id": "obj-13", "fontname": "<PERSON><PERSON>", "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadbang", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [50.0, 70.0, 58.0, 22.0], "numinlets": 1, "id": "obj-1", "fontname": "Arial Bold", "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "1. Get control surface path", "numoutlets": 0, "patching_rect": [190.0, 100.0, 150.0, 20.0], "numinlets": 1, "id": "obj-14", "fontname": "<PERSON><PERSON>", "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "path control_surfaces 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [50.0, 100.0, 127.0, 22.0], "numinlets": 2, "id": "obj-2", "fontname": "Arial Bold", "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [50.0, 130.0, 67.0, 22.0], "numinlets": 1, "id": "obj-3", "fontname": "Arial Bold", "fontsize": 10.0, "color": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "comment", "text": "2. Get Session button ID", "numoutlets": 0, "patching_rect": [190.0, 190.0, 150.0, 20.0], "numinlets": 1, "id": "obj-15", "fontname": "<PERSON><PERSON>", "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [50.0, 160.0, 32.5, 22.0], "numinlets": 1, "id": "obj-4", "fontname": "Arial Bold", "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Session_Mode_Button", "numoutlets": 1, "outlettype": [""], "patching_rect": [50.0, 190.0, 245.0, 22.0], "numinlets": 2, "id": "obj-5", "fontname": "Arial Bold", "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [50.0, 220.0, 62.0, 22.0], "numinlets": 2, "id": "obj-6", "fontname": "Arial Bold", "fontsize": 10.0, "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [50.0, 250.0, 155.0, 22.0], "numinlets": 1, "id": "obj-7", "fontname": "Arial Bold", "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "3. Observe button value", "numoutlets": 0, "patching_rect": [190.0, 310.0, 150.0, 20.0], "numinlets": 1, "id": "obj-16", "fontname": "<PERSON><PERSON>", "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [50.0, 280.0, 32.5, 22.0], "numinlets": 1, "id": "obj-8", "fontname": "Arial Bold", "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "property value", "numoutlets": 1, "outlettype": [""], "patching_rect": [50.0, 310.0, 79.0, 22.0], "numinlets": 2, "id": "obj-9", "fontname": "Arial Bold", "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.observer", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [50.0, 340.0, 72.0, 22.0], "numinlets": 2, "id": "obj-10", "fontname": "Arial Bold", "fontsize": 10.0, "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print session_button_value", "numoutlets": 0, "patching_rect": [50.0, 400.0, 140.0, 22.0], "numinlets": 1, "id": "obj-11", "fontname": "Arial Bold", "fontsize": 10.0}}], "lines": [{"patchline": {"source": ["obj-1", 0], "destination": ["obj-2", 0]}}, {"patchline": {"source": ["obj-2", 0], "destination": ["obj-3", 0]}}, {"patchline": {"source": ["obj-3", 1], "destination": ["obj-4", 0]}}, {"patchline": {"source": ["obj-4", 1], "destination": ["obj-6", 1]}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-5", 0]}}, {"patchline": {"source": ["obj-5", 0], "destination": ["obj-6", 0]}}, {"patchline": {"source": ["obj-6", 0], "destination": ["obj-7", 0]}}, {"patchline": {"source": ["obj-7", 0], "destination": ["obj-8", 0]}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-9", 0]}}, {"patchline": {"source": ["obj-8", 1], "destination": ["obj-10", 1]}}, {"patchline": {"source": ["obj-9", 0], "destination": ["obj-10", 0]}}, {"patchline": {"source": ["obj-10", 0], "destination": ["obj-11", 0]}}], "dependency_cache": [], "autosave": 0}}