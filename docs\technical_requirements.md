# Push2ClipQueue Technical Requirements

## 🚨 **CURRENT ISSUE**
**LED Problem:** Pad lights go dark when Session button is held. Need to implement LED preservation.
**Status:** ✅ Solution ready in `led-control-reference.maxpat` - needs integration into `clipQueue.maxpat`

## Core Functionality

1. **Session Button Detection**:
   - Monitor Session button (ID 51) using `Push-Get_A_Control_id` and `Push-Observe_A_Control_Value`
   - Set a toggle when Session is held
   - Release the toggle when Session is released

2. **Matrix Control Grabbing**:
   - Use `get_control_by_name Button_Matrix` to get matrix control ID
   - Use `Push-Release_Grab_A_Control` to intercept pad presses when Session is held
   - Prevent default clip launch behavior during queue mode
   - Release control when Session is released

3. **Clip Queueing Behavior**:
   - **Coordinate System**: (column, row) where column = track, row = scene
   - **Column-Unique Queueing**: Only one clip per track (column) can be queued at any time
   - **Multiple Clips Per Scene**: Multiple clips can be queued in the same scene (row) if they're in different tracks (columns)
   - **Track Conflict Resolution**: When a new clip is queued in a track that already has a queued clip, the new clip replaces the existing one
   - **Display Format**: "Clip at column, row" (e.g., "Clip at 2, 3" = track 2, scene 3)

4. **Technical Implementation**:
   - Monitor matrix button presses via live.object value observations
   - Filter button presses (velocity > 0) from button releases (velocity = 0)
   - Use **column-based indexing** for the collection to ensure track uniqueness
   - Store clip data using format: `store [column] [velocity] [row] [column]`
   - Update the queue display with all queued clips using dump mechanism
   - Display clips as formatted text: "Clip at column, row"

5. **Queue Display**:
   - Use `coll` object to store queued clips with column as index
   - Use `dump` command to get all stored clips for display
   - Format each clip entry and aggregate into a single display list
   - Clear display when queue is emptied

## Ableton Push 2 Session Mode Behavior

### Expected Behavior Examples

**Test Case 1: Same Track (Column), Different Scenes**
- Press clips at (0,0), (0,1), (0,2) = same track (column 0), different scenes
- **Expected Result**: Only last clip shown (e.g., "Clip at 0, 2")
- **Reason**: Only one clip per track allowed

**Test Case 2: Same Scene (Row), Different Tracks**  
- Press clips at (0,0), (1,0), (2,0) = different tracks (columns 0,1,2), same scene
- **Expected Result**: All clips shown (e.g., "Clip at 0, 0 Clip at 1, 0 Clip at 2, 0")
- **Reason**: Multiple clips per scene allowed if in different tracks

**Test Case 3: Mixed Scenario**
- Press clips at (0,0), (1,1), (0,2) = tracks 0,1,0 and scenes 0,1,2
- **Expected Result**: "Clip at 0, 2 Clip at 1, 1" (track 0 shows only latest clip)
- **Reason**: Track 0 conflict resolved by keeping latest clip

## Technical Implementation Details

### Data Flow Architecture

The system uses a carefully structured data flow:

1. **Matrix Data Input**:
   ```
   Push Matrix -> live.object -> gate (controlled by Session button)
   ```

2. **Queue Mode Processing**:
   ```
   gate outlet 1 -> unpack i i i i -> velocity filter -> pack i i i i -> sprintf -> coll
   ```

3. **Column-Based Indexing**:
   ```
   unpack -> column (outlet 2) -> sprintf inlet 0 (index)
   unpack -> velocity (outlet 0) -> sprintf inlet 1
   unpack -> row (outlet 1) -> sprintf inlet 2  
   unpack -> column (outlet 2) -> sprintf inlet 3
   ```

4. **Velocity Filtering**:
   ```
   velocity -> if $i1 > 0 then 1 else 0 -> gate control
   ```

4. **Collection Storage**:
   ```
   sprintf "store %d_%d %d %d" -> coll clip_queue -> triggers display update
   ```

5. **Display Update**:
   ```
   coll -> dump -> route -> unpack -> sprintf "Clip at %d,%d" -> append to display
   ```

### Critical Objects and Connections

1. **Gate Object (obj-33)**:
   - `gate 2 1` routes matrix data based on Session button state
   - Outlet 0: Normal mode (forward to Live)
   - Outlet 1: Queue mode (intercept for queueing)

2. **Velocity Filter (obj-91)**:
   - `if $i1 > 0 then bang` filters out button releases
   - Only button presses (velocity > 0) trigger queueing

3. **Pack Object (obj-106)**:
   - `pack i i i i` collects unpacked matrix data
   - Triggered by velocity filter bang
   - Sends complete data to sprintf

4. **Collection (obj-95)**:
   - `coll clip_queue` stores queued clips
   - Format: `x_y track velocity`
   - Triggers display update on each store operation

## Technical Components

1. **MIDI Monitoring**:
   - Use `midiin` to receive MIDI from Push 2
   - Filter for relevant control messages

2. **Control Surface Initialization**:
   - Verify live_path_out receives "id 1" (live set connection)
   - Verify control_path_out receives "id -4" (Push2 connection)
   - Connect control surface path to live.object second inlet
   - All live.object instances must have `_persistence : 1`
   - Monitor Max console for initialization success

3. **Control Message Sequence**:
   ```
   control_name 
     -> prepend call get_control 
     -> live.object (with path in second inlet)
     -> zl.filter get_control
   ```
   - Message order must be preserved exactly
   - Only send control messages after paths are established
   - Monitor live.object output for responses   - Control grabbing requires valid control ID response

5. **Matrix Control Grabbing Sequence**:
   ```
   loadbang -> path control_surfaces 0 -> live.path -> s ---PushPath
     |
   r ---PushPath -> live.object matrix control
     |
   Session button state -> Push-Release_Grab_A_Control
     |
   Matrix control ID -> Push-Release_Grab_A_Control
   ```
   - Push path must be established first
   - Matrix control ID must be resolved using `get_control_by_name Button_Matrix`
   - Session button state (1/0) controls grab/release timing
   - Grabbed control data flows through gate based on Session state

6. **Queue Processing Sequence**:
   ```
   Matrix data -> gate (Session controlled) -> unpack -> velocity filter -> pack -> sprintf -> coll
     |
   Collection -> dump -> display formatting -> queue list update
   ```
   - Only button presses (velocity > 0) are queued
   - Each queued clip stored with unique key format: `x_y`
   - Display shows all queued clips as "Clip at x,y" format

4. **Clip Storage**:
   - Use `coll` object to store queued clip coordinates
   - Format: `store x_y track velocity`
   - Key format ensures unique storage per pad position
   - Collection automatically triggers display updates

5. **Live API Integration**:
   - Use `live.path` to locate clip slots
   - Use `live.object` to interact with clips
   - Call `fire` method to launch clips

6. **User Interface**:
   - Display all queued clips in scrollable list
   - Show formatted clip positions: "Clip at x,y"
   - Provide Clear Queue button
   - Show status messages

## Performance Requirements

1. **Responsiveness**:
   - Button detection should be immediate
   - Clip queueing should be immediate
   - Clip launching should have minimal delay

2. **Reliability**:
   - Device should work consistently across different Live sets
   - Queue should be maintained even if Live's view changes
   - Clip launching should be reliable regardless of the current view
   - Control grabbing should consistently prevent default behavior

3. **Resource Usage**:
   - Minimal CPU usage
   - No memory leaks
   - Clean shutdown when device is removed

## Compatibility

1. **Hardware**:
   - Ableton Push 2

2. **Software**:
   - Ableton Live 10.0 or later
   - Max for Live 8.0 or later

3. **Integration**:
   - Compatible with other Max for Live devices
   - Minimal interference with normal Push 2 operation
   - Control grabbing should not affect other Push 2 functions
   - Clean release of controls when device is disabled