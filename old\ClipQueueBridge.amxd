{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [59.0, 106.0, 640.0, 480.0], "bglocked": 0, "openinpresentation": 1, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [30.0, 400.0, 74.0, 22.0], "text": "plugin~"}}, {"box": {"id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [30.0, 350.0, 74.0, 22.0], "text": "plugout~"}}, {"box": {"id": "obj-3", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [30.0, 30.0, 200.0, 20.0], "presentation": 1, "presentation_rect": [10.0, 10.0, 200.0, 20.0], "text": "Push 2 Clip <PERSON>"}}, {"box": {"id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [30.0, 60.0, 58.0, 22.0], "text": "loadbang"}}, {"box": {"id": "obj-5", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 90.0, 143.0, 22.0], "text": "path this_device"}}, {"box": {"id": "obj-6", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 120.0, 69.0, 22.0], "save": ["#N", "thispatcher", ";", "#Q", "end", ";"], "text": "thispatcher"}}, {"box": {"id": "obj-7", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 150.0, 85.0, 22.0], "text": "loadmess poll"}}, {"box": {"id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [30.0, 180.0, 111.0, 22.0], "text": "live.path live_set"}}, {"box": {"id": "obj-9", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [30.0, 210.0, 53.0, 22.0], "text": "live.object"}}, {"box": {"id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [150.0, 90.0, 40.0, 22.0], "text": "midiin"}}, {"box": {"id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 8, "outlettype": ["", "", "", "int", "int", "", "int", ""], "patching_rect": [150.0, 120.0, 92.5, 22.0], "text": "mid<PERSON><PERSON>e"}}, {"box": {"id": "obj-12", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [150.0, 180.0, 36.0, 22.0], "text": "sel 1"}}, {"box": {"id": "obj-13", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [150.0, 150.0, 24.0, 24.0]}}, {"box": {"id": "obj-14", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 180.0, 34.0, 22.0], "text": "gate"}}, {"box": {"id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [150.0, 210.0, 68.0, 22.0], "text": "route note"}}, {"box": {"id": "obj-16", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [150.0, 240.0, 142.0, 22.0], "text": "Push-Release_Grab_A_Control"}}, {"box": {"id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [300.0, 90.0, 70.0, 22.0], "text": "loadmess 0"}}, {"box": {"id": "obj-18", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [300.0, 120.0, 133.0, 22.0], "text": "Push-Get_A_Control_id"}}, {"box": {"id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [300.0, 150.0, 159.0, 22.0], "text": "Push-Observe_A_Control_Value"}}, {"box": {"id": "obj-20", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [300.0, 180.0, 36.0, 22.0], "text": "sel 1"}}, {"box": {"id": "obj-21", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [300.0, 210.0, 29.5, 22.0], "text": "51"}}, {"box": {"id": "obj-22", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [300.0, 240.0, 133.0, 22.0], "text": "Push-Get_A_Control_id"}}, {"box": {"id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [300.0, 270.0, 159.0, 22.0], "text": "Push-Observe_A_Control_Value"}}, {"box": {"id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [300.0, 300.0, 34.0, 22.0], "text": "gate"}}, {"box": {"id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [300.0, 330.0, 142.0, 22.0], "text": "M4L.api.SelectNextTrack"}}, {"box": {"id": "obj-26", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [30.0, 270.0, 150.0, 20.0], "presentation": 1, "presentation_rect": [10.0, 40.0, 150.0, 20.0], "text": "Queued Clips: 0"}}, {"box": {"id": "obj-27", "maxclass": "live.text", "mode": 0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", ""], "parameter_enable": 1, "patching_rect": [30.0, 300.0, 44.0, 15.0], "presentation": 1, "presentation_rect": [10.0, 70.0, 100.0, 20.0], "saved_attribute_attributes": {"valueof": {"parameter_enum": ["val1", "val2"], "parameter_longname": "live.text", "parameter_mmax": 1, "parameter_shortname": "live.text", "parameter_type": 2}}, "text": "Clear Queue", "varname": "live.text"}}], "lines": [{"patchline": {"destination": ["obj-11", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-15", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-24", 0]}}], "parameters": {"obj-7": ["live.text", "live.text", 0], "obj-8": ["live.text[1]", "live.text", 0], "obj-9": ["live.numbox", "live.numbox", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "inherited_shortname": 1}, "dependency_cache": [], "latency": 0, "is_mpe": 0, "minimum_live_version": "", "minimum_max_version": "", "platform_compatibility": 0, "project": {"version": 1, "creationdate": **********, "modificationdate": **********, "viewrect": [0.0, 0.0, 300.0, 500.0], "autoorganize": 1, "hideprojectwindow": 1, "showdependencies": 1, "autolocalize": 0, "contents": {"patchers": {}, "code": {}}, "layout": {}, "searchpath": {}, "detailsvisible": 0, "amxdtype": **********, "readonly": 0, "devpathtype": 0, "devpath": ".", "sortmode": 0, "viewmode": 0}, "autosave": 0}}