// Column-unique queue filter for Push2 ClipQueue
// Ensures only one clip per track (column) can be queued

var trackToIndex = {}; // Keep track of which index belongs to which track
var nextIndex = 0; // Counter for generating unique indices

// Initialization message
post("Column filter JavaScript loaded successfully\n");

function anything() {
    var args = arrayfromargs(arguments);
    
    post("column-filter received:", args, "\n");
    
    // Handle different message formats
    if (args.length >= 3) {
        // Check if it's a "store" command or raw integers
        if (args[0] === "store") {
            // Format: store index value track scene
            if (args.length >= 4) {
                var index = args[1];
                var value = args[2];
                var track = args[3];
                var scene = args.length > 4 ? args[4] : 0;
                
                post("Processing store command: index=" + index + " value=" + value + " track=" + track + " scene=" + scene + "\n");
                
                // Check if this track already has a clip queued
                if (trackToIndex.hasOwnProperty(track)) {
                    var oldIndex = trackToIndex[track];
                    post("Removing existing clip from track " + track + " at index " + oldIndex + "\n");
                    outlet(0, "remove", oldIndex);
                }
                
                // Use our own index counter instead of the incoming index
                var newIndex = nextIndex++;
                trackToIndex[track] = newIndex;
                post("Storing new clip: track " + track + " -> index " + newIndex + "\n");
                outlet(0, "store", newIndex, value, track, scene);
            }        } else if (typeof args[0] === "number") {
            // Format: index value track scene (raw integers)
            var incomingIndex = args[0];
            var value = args[1];
            var track = args[2];
            var scene = args.length > 3 ? args[3] : 0;
            
            post("Processing raw integers: incoming_index=" + incomingIndex + " value=" + value + " track=" + track + " scene=" + scene + "\n");
            
            // Only process button presses (value > 0), ignore button releases (value = 0)
            if (value > 0) {
                // Check if this track already has a clip queued
                if (trackToIndex.hasOwnProperty(track)) {
                    var oldIndex = trackToIndex[track];
                    post("Removing existing clip from track " + track + " at index " + oldIndex + "\n");
                    outlet(0, "remove", oldIndex);
                }
                  // Use our own index counter instead of the incoming index
                var newIndex = nextIndex++;
                trackToIndex[track] = newIndex;
                post("Storing new clip: track " + track + " -> index " + newIndex + "\n");
                post("Current tracked tracks:", Object.keys(trackToIndex), "\n");
                outlet(0, newIndex, value, track, scene);
            } else {
                post("Ignoring button release (value=0) for track " + track + "\n");
            }
        } else {
            // Pass through other commands
            outlet(0, args);
        }    } else if (args[0] === "clear") {
        // Clear our tracking when the collection is cleared
        post("Clearing all tracks and resetting index counter\n");
        post("Tracks before clear:", Object.keys(trackToIndex), "\n");
        trackToIndex = {};
        nextIndex = 0; // Reset index counter
        post("Sending clear command to collection\n");
        outlet(0, "clear");
        post("Clear command sent\n");
    } else if (args[0] === "remove") {
        // When removing by index, also clean up our tracking
        if (args.length >= 2) {
            var removeIndex = args[1];
            // Find and remove the track mapping for this index
            for (var track in trackToIndex) {
                if (trackToIndex[track] === removeIndex) {
                    delete trackToIndex[track];
                    break;
                }
            }
        }
        outlet(0, args);
    } else {
        // Pass through all other commands
        outlet(0, args);
    }
}

function clear() {
    post("Clear function called - resetting everything\n");
    trackToIndex = {};
    nextIndex = 0; // Reset index counter
    outlet(0, "clear");
}
