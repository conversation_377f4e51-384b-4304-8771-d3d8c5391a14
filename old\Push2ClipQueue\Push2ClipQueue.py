from __future__ import absolute_import, print_function, unicode_literals
from Push2.push2 import Push2
from Push2.session_component import SessionComponent
from _Framework.ButtonElement import ButtonElement
from _Framework.InputControlElement import MIDI_CC_TYPE
from _Framework.SubjectSlot import subject_slot

class Push2ClipQueue(Push2):
    def __init__(self, c_instance=None):
        super(Push2ClipQueue, self).__init__(c_instance=c_instance)
        self._queued_clips = []
        self._session_button_pressed = False
        self.log_message("Push2ClipQueue initialized")
        
    def disconnect(self):
        self._on_session_button_value.subject = None
        super(Push2ClipQueue, self).disconnect()
        
    def _create_session(self):
        session = super(Push2ClipQueue, self)._create_session()
        # Replace the session component with our custom one while preserving all settings
        new_session = QueueableSessionComponent(
            name=session.name,
            num_tracks=session.width(),
            num_scenes=session.height(),
            enable_skinning=True,
            is_enabled=session.is_enabled(),
            layer=session._layer)
        
        # Copy all the necessary attributes and settings
        new_session.set_rgb_mode(*session._rgb_mode)
        new_session.set_clip_launch_buttons(session._clip_launch_buttons)
        new_session.set_scene_launch_buttons(session._scene_launch_buttons)
        
        return new_session
        
    def _create_session_mode(self):
        session_mode = super(Push2ClipQueue, self)._create_session_mode()
        # Add our session button handler after the original mode is created
        self._on_session_button_value.subject = self._session_button
        return session_mode
        
    @subject_slot('value')
    def _on_session_button_value(self, value):
        self._session_button_pressed = bool(value)
        self.log_message("Session button: {}".format("pressed" if value else "released"))
        
    def _on_transport_play_value(self, value):
        super(Push2ClipQueue, self)._on_transport_play_value(value)
        if value and self._queued_clips:
            self._launch_queued_clips()
            
    def _launch_queued_clips(self):
        if not self._queued_clips:
            return
            
        self.log_message("Launching {} queued clips".format(len(self._queued_clips)))
        for clip_slot in self._queued_clips:
            if clip_slot and clip_slot.has_clip:
                clip_slot.fire()
        self._queued_clips = []


class QueueableSessionComponent(SessionComponent):
    def _on_clip_slot_button_value(self, value, x, y, is_momentary):
        push = self.canonical_parent
        if hasattr(push, '_session_button_pressed') and push._session_button_pressed and value:
            # Queue the clip instead of launching it
            clip_slot = self.scene(y).clip_slot(x)
            if clip_slot and clip_slot.has_clip:
                if clip_slot not in push._queued_clips:
                    push._queued_clips.append(clip_slot)
                    push.show_message("Queued clip at {}, {}".format(x+1, y+1))
                    push.log_message("Queued clip at {}, {}".format(x, y))
                    # Here you could send MIDI to your Max device to show visual feedback
        else:
            # Normal behavior when session button is not pressed
            super(QueueableSessionComponent, self)._on_clip_slot_button_value(value, x, y, is_momentary)
