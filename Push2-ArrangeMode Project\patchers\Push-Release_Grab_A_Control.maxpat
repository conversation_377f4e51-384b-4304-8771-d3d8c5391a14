{"patcher": {"fileversion": 1, "appversion": {"major": 6, "minor": 1, "revision": 9, "architecture": "x64"}, "rect": [105.0, 185.0, 557.0, 348.0], "bglocked": 0, "openinpresentation": 0, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 0, "gridsize": [15.0, 15.0], "gridsnaponopen": 0, "statusbarvisible": 2, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "boxes": [{"box": {"id": "obj-3", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [118.0, 59.0, 36.0, 36.0]}}, {"box": {"annotation": "send control id here", "comment": "send control id here", "hint": "send control id here", "id": "obj-1", "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [342.166626, 52.0, 25.0, 25.0]}}, {"box": {"bgcolor": [1.0, 1.0, 0.0, 1.0], "fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-92", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [272.333252, 255.5, 73.0, 18.0], "text": "r ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-93", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [272.333252, 282.414551, 51.0, 18.0], "text": "live.path"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-94", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [235.166626, 313.060059, 59.0, 18.0], "saved_object_attributes": {"_persistence": 1}, "text": "live.object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-95", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [135.0, 161.5, 88.166626, 29.0], "text": "prepend call release_control"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-96", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [342.166626, 161.5, 75.5, 29.0], "text": "prepend call grab_control"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-97", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [221.166626, 59.0, 33.0, 18.0], "text": "sel 0"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-99", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [50.0, 202.0, 104.0, 16.0], "text": "call release_control"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-100", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [235.166626, 202.0, 126.0, 16.0], "text": "call grab_control"}}, {"box": {"annotation": "send 0 or 1 here to grab/release", "comment": "send 0 or 1 here to grab/release", "hint": "send 0 or 1 here to grab/release", "id": "obj-102", "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [221.166626, 8.0, 25.0, 25.0]}}], "lines": [{"patchline": {"destination": ["obj-95", 0], "disabled": 0, "hidden": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-96", 0], "disabled": 0, "hidden": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-94", 0], "disabled": 0, "hidden": 0, "source": ["obj-100", 0]}}, {"patchline": {"destination": ["obj-3", 0], "disabled": 0, "hidden": 0, "source": ["obj-102", 0]}}, {"patchline": {"destination": ["obj-97", 0], "disabled": 0, "hidden": 0, "source": ["obj-102", 0]}}, {"patchline": {"destination": ["obj-93", 0], "disabled": 0, "hidden": 0, "source": ["obj-92", 0]}}, {"patchline": {"destination": ["obj-94", 1], "disabled": 0, "hidden": 0, "source": ["obj-93", 1]}}, {"patchline": {"destination": ["obj-99", 1], "disabled": 0, "hidden": 0, "source": ["obj-95", 0]}}, {"patchline": {"destination": ["obj-100", 1], "disabled": 0, "hidden": 0, "source": ["obj-96", 0]}}, {"patchline": {"destination": ["obj-100", 0], "disabled": 0, "hidden": 0, "source": ["obj-97", 1]}}, {"patchline": {"destination": ["obj-99", 0], "disabled": 0, "hidden": 0, "source": ["obj-97", 0]}}, {"patchline": {"destination": ["obj-94", 0], "disabled": 0, "hidden": 0, "source": ["obj-99", 0]}}], "dependency_cache": []}}