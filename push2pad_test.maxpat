{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 87.0, 640.0, 480.0], "bglocked": 0, "openinpresentation": 0, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"maxclass": "comment", "text": "Push2 Pad Test", "patching_rect": [30.0, 30.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-1"}}, {"box": {"maxclass": "comment", "text": "1. Initialize control surface", "patching_rect": [30.0, 60.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-2"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadbang", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [30.0, 90.0, 58.0, 22.0], "numinlets": 1, "id": "obj-3"}}, {"box": {"maxclass": "message", "text": "path live_set view control_surfaces 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 120.0, 185.0, 22.0], "numinlets": 2, "id": "obj-4"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [30.0, 150.0, 67.0, 22.0], "numinlets": 1, "id": "obj-5", "color": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "comment", "text": "2. Get Button Matrix control", "patching_rect": [30.0, 180.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-6"}}, {"box": {"maxclass": "message", "text": "call get_control Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 210.0, 150.0, 22.0], "numinlets": 2, "id": "obj-7"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 240.0, 62.0, 22.0], "numinlets": 2, "id": "obj-8", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 270.0, 98.0, 22.0], "numinlets": 1, "id": "obj-9"}}, {"box": {"maxclass": "comment", "text": "3. Observe matrix state", "patching_rect": [30.0, 300.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-10"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [30.0, 330.0, 32.5, 22.0], "numinlets": 1, "id": "obj-11"}}, {"box": {"maxclass": "message", "text": "property value", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 360.0, 79.0, 22.0], "numinlets": 2, "id": "obj-12"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.observer", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 390.0, 72.0, 22.0], "numinlets": 2, "id": "obj-13", "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print matrix_value", "numoutlets": 0, "patching_rect": [30.0, 420.0, 98.0, 22.0], "numinlets": 1, "id": "obj-14"}}], "lines": [{"patchline": {"source": ["obj-3", 0], "destination": ["obj-4", 0]}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-5", 0]}}, {"patchline": {"source": ["obj-5", 1], "destination": ["obj-8", 1]}}, {"patchline": {"source": ["obj-5", 1], "destination": ["obj-7", 0]}}, {"patchline": {"source": ["obj-7", 0], "destination": ["obj-8", 0]}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-9", 0]}}, {"patchline": {"source": ["obj-9", 0], "destination": ["obj-11", 0]}}, {"patchline": {"source": ["obj-11", 0], "destination": ["obj-12", 0]}}, {"patchline": {"source": ["obj-11", 1], "destination": ["obj-13", 1]}}, {"patchline": {"source": ["obj-12", 0], "destination": ["obj-13", 0]}}, {"patchline": {"source": ["obj-13", 0], "destination": ["obj-14", 0]}}], "dependency_cache": [], "autosave": 0}}