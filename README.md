# Push2ClipQueue - Production Ready ✅

A **fully functional** Max for Live device that adds comprehensive clip queueing functionality to the Ableton Push 2 controller.

## Features ✅ ALL COMPLETE

- **Clip Queueing:** Hold Session button to queue clips instead of launching immediately
- **Session + Play (Transport Stopped):** Immediate launch with no quantization + transport start
- **Session + Play (Transport Playing):** Quantized launch with no transport restart
- **Auto-Launch on Push Play:** Smart launch in both transport states with automatic detection
- **Perfect Synchronization:** Hybrid sync system ensures all clips launch together
- **Pad Light Preservation:** See which clips are available while Session button is held
- **UI Integration:** Real-time display updates, manual Launch/Clear buttons
- **Transport Safety:** No transport restarts during playback thanks to Play button blocking

## Installation

1. Place `clipQueue.maxpat` or `ClipQueue.amxd` on any track in Ableton Live
2. Ensure your Push 2 is connected and configured in Live's preferences
3. The device will automatically detect and connect to your Push 2

### Basic Operation
1. **Normal Mode:** Use Push 2 normally - no change in behavior
2. **Queue Clips:** Hold Session button and press pads to queue clips (LEDs show available clips)
3. **View Queue:** Check the device display to see queued clips

### Launch Methods

**Session + Play (Recommended):**
- Hold Session + Press Play for context-aware launching:
  - Transport stopped: Immediate launch + transport start
  - Transport playing: Quantized launch (no restart)

**Push Play Auto-Launch:**
- When clips are queued, pressing Play automatically launches them:
  - Transport stopped: Immediate launch + transport start  
  - Transport playing: Quantized launch (no restart)

**Manual Controls:**
- "Launch All Queued Clips" button: Launch manually anytime
- "Clear All Queued Clips" button: Clear queue without launching

## Technical Highlights

- **Hybrid Synchronization:** Primary `set_fire_button_state` with direct `fire()` fallback
- **Transport State Detection:** JavaScript-based detection for context-aware behavior
- **Play Button Blocking:** Prevents transport interference during Session mode
- **Session-Aware Filtering:** Prevents conflicts between different launch methods

## Version

**Current:** v6.5 - Production Ready  
**Status:** ✅ **COMPLETE AND FULLY FUNCTIONAL**

## Documentation

- **Complete Feature Documentation:** `docs/feature_status_matrix.md`
- **System Documentation:** `docs/system_documentation.md`
- **Technical Requirements:** `docs/technical_requirements.md`

## Files

- **Main Device:** `clipQueue.maxpat`
- **JavaScript Engine:** `clipLauncher.js` (v6.5)
- **Column Filter:** `column-filter.js`
- **Compiled Device:** `build/ClipQueue.amxd`

## Credits

- Inspired by the [Push2-ArrangeMode](https://github.com/leolabs/Push2-ArrangeMode) project
- Uses components from the Max for Live API

**The system is now fully operational with all requested features working perfectly.**

## License

MIT

