{"patcher": {"fileversion": 1, "appversion": {"major": 6, "minor": 1, "revision": 3, "architecture": "x86"}, "rect": [353.0, 232.0, 342.0, 344.0], "bglocked": 0, "openinpresentation": 0, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 0, "gridsize": [15.0, 15.0], "gridsnaponopen": 0, "statusbarvisible": 2, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "boxes": [{"box": {"annotation": "send <PERSON><PERSON><PERSON><PERSON> here", "comment": "send <PERSON><PERSON><PERSON><PERSON> here", "hint": "send <PERSON><PERSON><PERSON><PERSON> here", "id": "obj-1", "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [218.0, 40.0, 25.0, 25.0], "presentation_rect": [197.0, 39.0, 0.0, 0.0]}}, {"box": {"bgcolor": [1.0, 1.0, 0.0, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-2", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [166.0, 178.5, 129.0, 18.0], "text": "id -26"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-8", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [98.0, 238.0, 50.0, 18.0], "text": "id -39"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [50.0, 100.0, 127.0, 18.0], "text": "prepend call get_control"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-49", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [50.0, 178.5, 59.0, 18.0], "saved_object_attributes": {"_persistence": 1}, "text": "live.object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-50", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [74.0, 153.914551, 51.0, 18.0], "text": "live.path"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-45", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [50.0, 207.5, 100.0, 18.0], "text": "zl.filter get_control"}}, {"box": {"annotation": "send control name here", "comment": "send control name here", "hint": "send control name here", "id": "obj-17", "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [50.0, 40.0, 25.0, 25.0]}}, {"box": {"annotation": "action message (just to check)", "comment": "action message (just to check)", "hint": "action message (just to check)", "id": "obj-18", "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [282.0, 127.0, 25.0, 25.0]}}, {"box": {"annotation": "control id out", "comment": "control id out", "hint": "control id out", "id": "obj-19", "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [50.0, 278.0, 25.0, 25.0]}}], "lines": [{"patchline": {"destination": ["obj-50", 0], "disabled": 0, "hidden": 0, "midpoints": [227.5, 139.957275, 83.5, 139.957275], "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-3", 0], "disabled": 0, "hidden": 0, "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-18", 0], "disabled": 0, "hidden": 0, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-49", 0], "disabled": 0, "hidden": 0, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-19", 0], "disabled": 0, "hidden": 0, "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-8", 1], "disabled": 0, "hidden": 0, "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-45", 0], "disabled": 0, "hidden": 0, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-2", 1], "disabled": 0, "hidden": 0, "source": ["obj-50", 1]}}, {"patchline": {"destination": ["obj-49", 1], "disabled": 0, "hidden": 0, "source": ["obj-50", 1]}}]}}