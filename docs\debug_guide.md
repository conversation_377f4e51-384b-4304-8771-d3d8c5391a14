# Clip Queuing Debug Guide

## IMPORTANT UPDATE
The clip queuing system has been updated to use `fire()` instead of `set_fire_button_state` due to API compatibility issues. 

## Setup
1. Open Ableton Live with a session that has clips
2. Make sure there's at least one clip in track 0, clip slot 0 (first track, first scene)
3. Set Launch Quantization to 1 Bar or higher in Live to see queuing behavior
4. Open the Max patch (clipQueue.maxpat) in Max

## Debug Steps

### Step 1: Inspect Current State
Click the "inspect_clip 0 0" button in the Max patch presentation view
- This will show the current state of track 0, clip slot 0
- Look for `is_triggered` values
- Check if the clip exists (`has_clip` should be [1])

### Step 2: Test Queuing
Click the "test_queuing 0 0" button in the Max patch presentation view
- This runs a comprehensive test of the queuing system
- It tries both ClipSlot and Clip object approaches using `fire()`
- Watch the Max console for detailed output

### Step 3: Visual Verification
After running the test:
- Look at Ableton Live interface
- Check if the clip in track 0, scene 0 is pulsing/blinking
- The clip launch button should show a pulsing state if queuing worked

## Expected Behavior
If `fire()` works correctly with quantization:
- `is_triggered` should change from [0] to [1]
- The clip should visually pulse in Ableton Live
- Calling `fire()` again should launch the clip immediately

## Troubleshooting
If clips don't pulse:
1. **Check Launch Quantization**: Set to 1 Bar or higher in Live
2. Check if the clip exists and is accessible
3. Verify the LiveAPI path is correct
4. Try different clips or tracks
5. Check if Live is playing (clip queuing only works when playing)

## Key Debug Output to Watch For
- "LiveAPI object created successfully" - confirms clip access
- "has_clip result: [1]" - confirms clip exists
- "fire() call completed" - confirms API call worked
- "New is_triggered after fire(): [1]" - confirms queuing state activated
- "SUCCESS: Clip is now in triggered/pulsing state!" - confirms success

## Manual Testing Alternative
You can also test by:
1. Setting Launch Quantization to 1 Bar in Ableton Live
2. Pressing play in Live
3. Clicking a clip - it should pulse instead of launching immediately
4. This confirms that queuing/pulsing is working in Live

## Audio Blocking Fixed
The JavaScript now ignores "signal" messages to prevent audio blocking. The patch should no longer interfere with audio routing.
