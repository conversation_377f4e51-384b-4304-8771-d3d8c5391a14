{"patcher": {"fileversion": 1, "rect": [37.0, 55.0, 935.0, 499.0], "bglocked": 0, "defrect": [37.0, 55.0, 935.0, 499.0], "openrect": [0.0, 0.0, 0.0, 0.0], "openinpresentation": 0, "default_fontsize": 10.0, "default_fontface": 0, "default_fontname": "Arial Bold Italic", "gridonopen": 0, "gridsize": [8.0, 8.0], "gridsnaponopen": 0, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "boxes": [{"box": {"maxclass": "comment", "text": "Simplified control of Live object functions using the Live API", "fontname": "Arial Bold Italic", "numinlets": 1, "numoutlets": 0, "patching_rect": [536.0, 48.0, 347.0, 19.0], "fontsize": 11.0, "id": "obj-10"}}, {"box": {"maxclass": "comment", "text": "ID and path data to be passed to other objects", "linecount": 2, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [776.0, 408.0, 120.0, 29.0], "fontsize": 10.0, "id": "obj-9"}}, {"box": {"maxclass": "comment", "text": "Messages from live.object", "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [216.0, 400.0, 126.0, 18.0], "fontsize": 10.0, "id": "obj-6"}}, {"box": {"maxclass": "comment", "text": "Messages to live.object", "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [216.0, 344.0, 114.0, 18.0], "fontsize": 10.0, "id": "obj-7"}}, {"box": {"maxclass": "comment", "text": "Sends a bang when all attributes are loaded", "linecount": 3, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [176.0, 160.0, 92.0, 41.0], "fontsize": 10.0, "id": "obj-2"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend id", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "patching_rect": [776.0, 272.0, 62.0, 18.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-5"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "substitute 0", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 2, "patching_rect": [496.0, 160.0, 67.0, 18.0], "fontsize": 10.0, "outlettype": ["", ""], "id": "obj-4"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "substitute bang 0", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 2, "patching_rect": [568.0, 160.0, 93.0, 18.0], "fontsize": 10.0, "outlettype": ["", ""], "id": "obj-1"}}, {"box": {"maxclass": "comment", "text": "path", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [835.0, 383.0, 32.0, 18.0], "fontsize": 10.0, "id": "obj-78"}}, {"box": {"maxclass": "outlet", "prototypename": "M4L.Arial10", "numinlets": 1, "numoutlets": 0, "patching_rect": [816.0, 383.0, 18.0, 18.0], "id": "obj-81", "comment": "ID"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "fontname": "Arial Bold", "numinlets": 2, "color": [0.984314, 0.819608, 0.05098, 1.0], "numoutlets": 1, "patching_rect": [816.0, 351.0, 59.0, 18.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-76", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "comment", "text": "ID", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [795.0, 383.0, 20.0, 18.0], "fontsize": 10.0, "id": "obj-72"}}, {"box": {"maxclass": "outlet", "prototypename": "M4L.Arial10", "numinlets": 1, "numoutlets": 0, "patching_rect": [776.0, 383.0, 18.0, 18.0], "id": "obj-73", "comment": "ID"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t l getpath l", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 3, "patching_rect": [776.0, 329.0, 99.0, 18.0], "fontsize": 10.0, "outlettype": ["", "", ""], "id": "obj-41"}}, {"box": {"maxclass": "comment", "text": "Some functions may return values", "linecount": 2, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [456.0, 453.0, 96.0, 29.0], "fontsize": 10.0, "id": "obj-179"}}, {"box": {"maxclass": "comment", "text": "Live.function", "frgb": [0.301961, 0.337255, 0.403922, 1.0], "fontname": "Arial Bold Italic", "numinlets": 1, "textcolor": [0.301961, 0.337255, 0.403922, 1.0], "numoutlets": 0, "patching_rect": [536.0, 24.0, 123.0, 27.0], "fontsize": 18.0, "id": "obj-176"}}, {"box": {"maxclass": "comment", "text": "Path", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [584.0, 136.0, 32.0, 18.0], "fontsize": 10.0, "id": "obj-130"}}, {"box": {"maxclass": "comment", "text": "Return", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [512.0, 432.0, 43.0, 18.0], "fontsize": 10.0, "id": "obj-128"}}, {"box": {"maxclass": "outlet", "prototypename": "M4L.Arial10", "numinlets": 1, "numoutlets": 0, "patching_rect": [488.0, 432.0, 18.0, 18.0], "id": "obj-129", "comment": "Return"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "patching_rect": [640.0, 287.0, 50.0, 18.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-125"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend set", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "patching_rect": [424.0, 160.0, 67.0, 18.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-124"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend call", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "patching_rect": [352.0, 304.0, 69.0, 18.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-123"}}, {"box": {"maxclass": "comment", "text": "<PERSON>", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [371.0, 136.0, 35.0, 18.0], "fontsize": 10.0, "id": "obj-54"}}, {"box": {"maxclass": "button", "numinlets": 1, "numoutlets": 1, "blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "patching_rect": [352.0, 136.0, 18.0, 18.0], "outlettype": ["bang"], "id": "obj-53"}}, {"box": {"maxclass": "button", "numinlets": 1, "numoutlets": 1, "blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "patching_rect": [280.0, 167.0, 18.0, 18.0], "outlettype": ["bang"], "id": "obj-122"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route start", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "patching_rect": [280.0, 136.0, 60.0, 18.0], "fontsize": 10.0, "outlettype": ["", ""], "id": "obj-121"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl reg", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 2, "patching_rect": [352.0, 184.0, 37.0, 18.0], "fontsize": 10.0, "outlettype": ["", ""], "id": "obj-120"}}, {"box": {"maxclass": "comment", "text": "Arg: function", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [96.0, 112.0, 79.0, 18.0], "fontsize": 10.0, "id": "obj-112"}}, {"box": {"maxclass": "comment", "text": "Messages", "frgb": [0.301961, 0.337255, 0.403922, 1.0], "fontname": "Arial Bold Italic", "numinlets": 1, "textcolor": [0.301961, 0.337255, 0.403922, 1.0], "numoutlets": 0, "patching_rect": [72.0, 197.0, 68.0, 20.0], "fontsize": 12.0, "id": "obj-109"}}, {"box": {"maxclass": "comment", "text": "Optional function values", "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 416.0, 117.0, 18.0], "fontsize": 10.0, "id": "obj-107"}}, {"box": {"maxclass": "comment", "text": "anything", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 400.0, 54.0, 18.0], "fontsize": 10.0, "id": "obj-108"}}, {"box": {"maxclass": "comment", "text": "<start>, data..., <done>. Required for some MIDI note operations", "linecount": 2, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 368.0, 162.0, 29.0], "fontsize": 10.0, "id": "obj-105"}}, {"box": {"maxclass": "comment", "text": "sequence", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 352.0, 57.0, 18.0], "fontsize": 10.0, "id": "obj-106"}}, {"box": {"maxclass": "comment", "text": "path", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 320.0, 32.0, 18.0], "fontsize": 10.0, "id": "obj-102"}}, {"box": {"maxclass": "comment", "text": "The id of the object", "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 304.0, 106.0, 18.0], "fontsize": 10.0, "id": "obj-99"}}, {"box": {"maxclass": "comment", "text": "id", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 288.0, 19.0, 18.0], "fontsize": 10.0, "id": "obj-100"}}, {"box": {"maxclass": "comment", "text": "bang", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 224.0, 35.0, 18.0], "fontsize": 10.0, "id": "obj-95"}}, {"box": {"maxclass": "comment", "text": "< Anything (value)", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [656.0, 136.0, 97.0, 18.0], "fontsize": 10.0, "id": "obj-93"}}, {"box": {"maxclass": "message", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "patching_rect": [560.0, 355.0, 44.0, 16.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-85"}}, {"box": {"maxclass": "message", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "patching_rect": [216.0, 416.0, 155.0, 16.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-84"}}, {"box": {"maxclass": "comment", "text": "Path", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [640.0, 363.0, 32.0, 18.0], "fontsize": 10.0, "id": "obj-82"}}, {"box": {"maxclass": "comment", "text": "ID", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [608.0, 355.0, 20.0, 18.0], "fontsize": 10.0, "id": "obj-80"}}, {"box": {"maxclass": "comment", "text": "ID", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [475.0, 136.0, 20.0, 18.0], "fontsize": 10.0, "id": "obj-79"}}, {"box": {"maxclass": "message", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "patching_rect": [560.0, 379.0, 160.0, 16.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-77"}}, {"box": {"maxclass": "number", "prototypename": "Live", "triscale": 0.75, "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "patching_rect": [496.0, 136.0, 40.0, 18.0], "fontsize": 10.0, "outlettype": ["int", "bang"], "id": "obj-74"}}, {"box": {"maxclass": "message", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "patching_rect": [216.0, 360.0, 155.0, 16.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-66"}}, {"box": {"maxclass": "comment", "text": "The function of the object to fire", "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 272.0, 157.0, 18.0], "fontsize": 10.0, "id": "obj-61"}}, {"box": {"maxclass": "comment", "text": "function", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 256.0, 50.0, 18.0], "fontsize": 10.0, "id": "obj-60"}}, {"box": {"maxclass": "comment", "text": "Fire the selected function", "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 240.0, 127.0, 18.0], "fontsize": 10.0, "id": "obj-55"}}, {"box": {"maxclass": "comment", "text": "Done", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [176.0, 136.0, 35.0, 18.0], "fontsize": 10.0, "id": "obj-18"}}, {"box": {"maxclass": "button", "numinlets": 1, "numoutlets": 1, "blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "patching_rect": [208.0, 136.0, 18.0, 18.0], "outlettype": ["bang"], "id": "obj-47"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route done sequence bang function id path", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 7, "patching_rect": [208.0, 112.0, 451.0, 18.0], "fontsize": 10.0, "outlettype": ["", "", "", "", "", "", ""], "id": "obj-43"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend id", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "patching_rect": [496.0, 248.0, 62.0, 18.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-23"}}, {"box": {"maxclass": "inlet", "prototypename": "M4L.Arial10", "numinlets": 0, "numoutlets": 1, "patching_rect": [304.0, 24.0, 18.0, 18.0], "outlettype": [""], "id": "obj-33", "comment": "Path or ID"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend function", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "patching_rect": [96.0, 95.0, 92.0, 18.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-34"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend path", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "patching_rect": [568.0, 248.0, 74.0, 18.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-49"}}, {"box": {"maxclass": "comment", "text": "Attributes (= messages)", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [72.0, 24.0, 124.0, 18.0], "fontsize": 10.0, "id": "obj-50"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "patcherargs", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "patching_rect": [96.0, 40.0, 68.0, 18.0], "fontsize": 10.0, "outlettype": ["", ""], "id": "obj-56"}}, {"box": {"maxclass": "comment", "text": "< Messages >", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 0, "patching_rect": [227.0, 24.0, 76.0, 18.0], "fontsize": 10.0, "id": "obj-65"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "fontname": "Arial Bold", "numinlets": 1, "color": [0.984314, 0.819608, 0.05098, 1.0], "numoutlets": 3, "patching_rect": [568.0, 312.0, 53.0, 18.0], "fontsize": 10.0, "outlettype": ["", "", ""], "id": "obj-69"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl slice 1", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 2, "patching_rect": [456.0, 392.0, 51.0, 18.0], "fontsize": 10.0, "outlettype": ["", ""], "id": "obj-94"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "fontname": "Arial Bold", "numinlets": 2, "color": [0.984314, 0.819608, 0.05098, 1.0], "numoutlets": 1, "patching_rect": [456.0, 360.0, 59.0, 18.0], "fontsize": 10.0, "outlettype": [""], "id": "obj-96", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "inlet", "prototypename": "M4L.Arial10", "numinlets": 0, "numoutlets": 1, "patching_rect": [208.0, 24.0, 18.0, 18.0], "outlettype": [""], "id": "obj-119", "comment": "property <symbol>, observe <1/0>, get, bang (get), set <property>, anything (property data)"}}, {"box": {"maxclass": "panel", "rounded": 16, "border": 1, "bgcolor": [0.094118, 0.113725, 0.137255, 0.0], "numinlets": 1, "grad2": [0.415686, 0.454902, 0.52549, 1.0], "numoutlets": 0, "patching_rect": [24.0, 216.0, 176.0, 229.0], "id": "obj-46"}}, {"box": {"maxclass": "comment", "text": "The path of the object", "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 336.0, 122.0, 18.0], "fontsize": 10.0, "id": "obj-3"}}, {"box": {"maxclass": "comment", "text": "< For clarity and convenience, you may want to use one inlet to set messages, and another inlet to set values.", "linecount": 3, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [328.0, 24.0, 193.0, 41.0], "fontsize": 10.0, "id": "obj-8"}}], "lines": [{"patchline": {"source": ["obj-124", 0], "destination": ["obj-125", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-1", 1], "destination": ["obj-49", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-1", 0], "destination": ["obj-5", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-43", 5], "destination": ["obj-1", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-5", 0], "destination": ["obj-41", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-5", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 1], "destination": ["obj-23", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-74", 0], "destination": ["obj-4", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-43", 3], "destination": ["obj-120", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-43", 0], "destination": ["obj-47", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-33", 0], "destination": ["obj-43", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-119", 0], "destination": ["obj-43", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-56", 1], "destination": ["obj-43", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-43", 4], "destination": ["obj-74", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-43", 1], "destination": ["obj-121", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-43", 2], "destination": ["obj-53", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-43", 3], "destination": ["obj-124", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-43", 6], "destination": ["obj-125", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-34", 0], "destination": ["obj-43", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-94", 1], "destination": ["obj-129", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-49", 0], "destination": ["obj-77", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-69", 1], "destination": ["obj-96", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-23", 0], "destination": ["obj-96", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-125", 0], "destination": ["obj-123", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-120", 0], "destination": ["obj-123", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-123", 0], "destination": ["obj-66", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-123", 0], "destination": ["obj-96", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-121", 1], "destination": ["obj-123", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-53", 0], "destination": ["obj-120", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-122", 0], "destination": ["obj-120", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-121", 0], "destination": ["obj-122", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-69", 1], "destination": ["obj-85", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-96", 0], "destination": ["obj-84", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-49", 0], "destination": ["obj-69", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-96", 0], "destination": ["obj-94", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-76", 0], "destination": ["obj-81", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-41", 2], "destination": ["obj-76", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-41", 1], "destination": ["obj-76", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-41", 0], "destination": ["obj-73", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-69", 1], "destination": ["obj-41", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-56", 0], "destination": ["obj-34", 0], "hidden": 0, "midpoints": []}}]}}