{"patcher": {"fileversion": 1, "rect": [129.0, 44.0, 452.0, 412.0], "bglocked": 0, "defrect": [129.0, 44.0, 452.0, 412.0], "openrect": [0.0, 0.0, 0.0, 0.0], "openinpresentation": 0, "default_fontsize": 10.0, "default_fontface": 0, "default_fontname": "Arial Bold", "gridonopen": 0, "gridsize": [8.0, 8.0], "gridsnaponopen": 0, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "boxes": [{"box": {"maxclass": "comment", "text": "< if the track index is not part of our visible track list we assume that we are to the right of our track list and so we select the last track of the visible track list which has an index of TrackCount - 1", "linecount": 5, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "patching_rect": [232.0, 224.0, 218.0, 64.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-11"}}, {"box": {"maxclass": "button", "prototypename": "M4L.patching", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [40.0, 120.0, 18.0, 18.0], "id": "obj-8"}}, {"box": {"maxclass": "comment", "prototypename": "ML.subpatcher-title", "text": "Select Previous Track", "fontname": "Arial Bold Italic", "numinlets": 1, "numoutlets": 0, "fontsize": 24.0, "patching_rect": [16.0, 16.0, 259.0, 34.0], "textcolor": [0.3, 0.34, 0.4, 1.0], "frgb": [0.3, 0.34, 0.4, 1.0], "id": "obj-48"}}, {"box": {"maxclass": "comment", "prototypename": "<PERSON><PERSON><PERSON>patcher-story", "text": "<PERSON> selects the previous track in the current Live Set.", "fontname": "Arial Italic", "numinlets": 1, "numoutlets": 0, "fontsize": 11.0, "patching_rect": [16.0, 48.0, 277.0, 19.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-50"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t i b", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0, "outlettype": ["int", "bang"], "patching_rect": [40.0, 256.0, 33.0, 18.0], "id": "obj-4"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t i i i", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 3, "fontsize": 10.0, "outlettype": ["int", "int", "int"], "patching_rect": [40.0, 192.0, 181.0, 18.0], "id": "obj-2"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "int", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": ["int"], "patching_rect": [184.0, 264.0, 37.0, 18.0], "id": "obj-1"}}, {"box": {"maxclass": "message", "text": "0", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [81.0, 288.0, 31.0, 16.0], "id": "obj-13"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "- 1", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": ["int"], "patching_rect": [40.0, 168.0, 32.0, 18.0], "id": "obj-12"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "clip", "fontname": "Arial Bold", "numinlets": 3, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [40.0, 320.0, 100.0, 18.0], "id": "obj-10"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "- 1", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": ["int"], "patching_rect": [40.0, 288.0, 32.0, 18.0], "id": "obj-5"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "M4L.api.SetSelectedTrackIndex", "fontname": "Arial Bold", "numinlets": 1, "color": [0.545098, 0.85098, 0.592157, 1.0], "numoutlets": 0, "fontsize": 10.0, "patching_rect": [104.0, 368.0, 161.0, 18.0], "id": "obj-7"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "M4L.api.GetVisibleTrackCount", "fontname": "Arial Bold", "numinlets": 1, "color": [0.545098, 0.85098, 0.592157, 1.0], "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [40.0, 144.0, 155.0, 18.0], "id": "obj-6"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "M4L.api.GetSelectedTrackIndex", "fontname": "Arial Bold", "numinlets": 1, "color": [0.545098, 0.85098, 0.592157, 1.0], "numoutlets": 2, "fontsize": 10.0, "outlettype": ["int", "bang"], "patching_rect": [40.0, 224.0, 163.0, 18.0], "id": "obj-3"}}, {"box": {"maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [40.0, 88.0, 18.0, 18.0], "id": "obj-23", "comment": ""}}], "lines": [{"patchline": {"source": ["obj-3", 0], "destination": ["obj-4", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-3", 1], "destination": ["obj-1", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-2", 0], "destination": ["obj-3", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-23", 0], "destination": ["obj-8", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-13", 0], "destination": ["obj-10", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-5", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 1], "destination": ["obj-13", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-2", 1], "destination": ["obj-10", 2], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-2", 2], "destination": ["obj-1", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-12", 0], "destination": ["obj-2", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-5", 0], "destination": ["obj-10", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-6", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-6", 0], "destination": ["obj-12", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-10", 0], "destination": ["obj-7", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-1", 0], "destination": ["obj-7", 0], "hidden": 0, "midpoints": []}}]}}