// clipLauncher.js - Simplified clip launcher for Push2ClipQueue
// Based on the Launcher.js from the launcher project
// VERSION: v6.6 - SESSION + PLAY SYSTEM: PRODUCTION READY

inlets = 3;  // inlet 0: commands, inlet 1: collection data, inlet 2: dump complete bang
outlets = 1;

var expecting_dump = false;
var accumulated_clips = [];
var dump_just_triggered = false;
var dump_timeout = null;
var recent_clips = []; // Store recent collection outputs
var launch_when_dumped = false; // Flag to indicate we should launch after dump

// PAD LIGHT PRESERVATION SYSTEM
var session_mode_active = false;
var original_pad_states = {}; // Store original clip states for restoration
var led_update_task = null; // Task for periodic LED updates

// Observe clip states for pad lighting
var clip_observers = [];
var scene_count = 8;
var track_count = 8;

// Log initialization to verify correct version is loaded
post("*** clipLauncher.js v7.6 LOADED - SESSION + PLAY + EXISTING BUTTON_MATRIX CONTROL ***\n");

function log() {
    for(var i=0,len=arguments.length; i<len; i++) {
        var message = arguments[i];
        if(message && message.toString) {
            var s = message.toString();
            if(s.indexOf("[object ") >= 0) {
                s = JSON.stringify(message);
            }
            post(s);
        }
        else if(message === null) {
            post("<null>");
        }
        else {
            post(message);
        }
    }
    post("\n");
}

// Utility function to check quantization settings for queuing (no longer forces changes)
function ensure_quantization_for_queuing() {
    log("=== CHECKING QUANTIZATION FOR QUEUING ===");
    
    try {
        var sessionObject = new LiveAPI("live_set");
        if (sessionObject && sessionObject.id != 0) {
            // Check session-level quantization but don't force changes
            var session_quant = sessionObject.get("clip_trigger_quantization");
            log("Current session clip_trigger_quantization:", session_quant);
            
            if (session_quant && session_quant.length > 0) {
                var quant_value = session_quant[0];
                
                if (quant_value == 0) {
                    log("Quantization is set to IMMEDIATE - clips will launch together on play/start");
                } else {
                    var quant_names = ["Immediate", "8 Bars", "4 Bars", "2 Bars", "1 Bar", "1/2 Note", "1/4 Note", "1/8 Note", "1/16 Note"];
                    var quant_name = quant_names[quant_value] || ("Value " + quant_value);
                    log("Quantization is set to:", quant_name, "- clips will sync to this boundary");
                }
            } else {
                log("Could not read session quantization setting");
            }
            
            // Check if session is playing - queuing behavior may differ
            var is_playing = sessionObject.get("is_playing");
            log("Session is_playing:", is_playing);
            
            return true;
        } else {
            log("Could not access live_set for quantization check");
            return false;
        }
    } catch (error) {
        log("Error in ensure_quantization_for_queuing:", error.toString());
        return false;
    }
}

// Launch a single clip using internal queuing given track and clip slot numbers
function launch_clip(track_num, clip_slot_num) {
    try {
        var path = "live_set tracks " + track_num + " clip_slots " + clip_slot_num;
        log("Creating LiveAPI with path:", path);
        var liveObject = new LiveAPI(path);
        
        if (liveObject && liveObject.id != 0) {
            log("LiveAPI object created successfully, ID:", liveObject.id);
            var has_clip = liveObject.get("has_clip");
            log("has_clip result:", has_clip);
            if (has_clip && has_clip[0] == 1) {
                // Check current is_triggered state
                var current_triggered = liveObject.get("is_triggered");
                log("Current is_triggered:", current_triggered);                // CRITICAL: Check quantization and handle immediate vs. quantized differently
                var sessionObject = new LiveAPI("live_set");
                var global_quantization = 0; // Default to immediate if we can't read it
                
                if (sessionObject && sessionObject.id != 0) {
                    var session_quant = sessionObject.get("clip_trigger_quantization");
                    log("Current session clip_trigger_quantization:", session_quant);
                    
                    if (session_quant && session_quant.length > 0) {
                        global_quantization = session_quant[0];
                        
                        if (global_quantization == 0) {
                            log("*** QUANTIZATION IS IMMEDIATE - CLIPS WILL NOT QUEUE FOR SYNC ***");
                            log("*** IMMEDIATE MODE: set_fire_button_state will launch clips instantly ***");
                            log("*** FOR PERFECT SYNC WITH IMMEDIATE, WE NEED TO QUEUE DIFFERENTLY ***");
                        } else {
                            log("*** RESPECTING EXISTING QUANTIZATION SETTING:", global_quantization, "***");
                            log("All clips will sync to this quantization boundary");
                        }
                    }
                }                // CORRECT APPROACH: Handle immediate vs. quantized differently for perfect sync
                // When quantization is immediate (0), set_fire_button_state launches instantly
                // When quantization is > 0, set_fire_button_state queues for sync launch
                log("*** CLIP QUEUING STRATEGY *** on track", track_num, "slot", clip_slot_num);
                
                if (global_quantization == 0) {
                    // IMMEDIATE QUANTIZATION: Don't use set_fire_button_state - it launches instantly
                    // Instead, we'll rely on the global launch mechanism to fire all clips together
                    log("*** IMMEDIATE MODE: NOT calling set_fire_button_state (would launch instantly) ***");
                    log("*** Clip will be launched via global launch command for perfect sync ***");
                    // Just mark as successfully queued - actual launch happens in launch_queued_clips
                } else {
                    // QUANTIZED MODE: Use set_fire_button_state for proper queuing
                    log("*** QUANTIZED MODE: Using set_fire_button_state for sync queuing ***");
                    try {
                        // CRITICAL: Use set_fire_button_state(1) to queue clip for synchronized launch
                        // All clips queued this way will launch at the same quantization boundary
                        liveObject.call("set_fire_button_state", 1);
                        log("*** set_fire_button_state 1 CALL COMPLETED ***");
                        
                        // Verify the state was set with a small delay
                        var task = new Task(function() {
                            var new_triggered = liveObject.get("is_triggered");
                            log("New is_triggered after set_fire_button_state 1:", new_triggered);
                            
                            if (new_triggered && new_triggered[0] == 1) {
                                log("SUCCESS: Clip is now in triggered/pulsing state with perfect sync!");
                                log("Clip will launch at next quantization boundary for perfect synchronization");
                            } else {
                                log("INFO: set_fire_button_state 1 called but clip not showing triggered state");
                                log("This may happen if transport is stopped - clip should still queue properly");
                            }
                        });
                        task.schedule(50); // Check after 50ms
                    } catch (fire_error) {
                        log("ERROR calling set_fire_button_state:", fire_error.toString());
                        return false;
                    }
                }
                
                log("Successfully attempted to queue clip at track", track_num, "slot", clip_slot_num);
                return true;
            } else {
                log("No clip found at track", track_num, "slot", clip_slot_num, "- has_clip:", has_clip);
                return false;
            }
        } else {
            log("Could not access clip slot at track", track_num, "slot", clip_slot_num, "- LiveAPI ID:", liveObject ? liveObject.id : "null");
            return false;
        }
    } catch (error) {
        log("Error queuing clip at track", track_num, "slot", clip_slot_num, ":", error.toString());
        return false;
    }
}

// Handle dump command - prepares to receive collection data
function dump() {
    log("Dump command received - preparing to receive collection data");
    log("Before: expecting_dump =", expecting_dump);
    expecting_dump = true;
    dump_just_triggered = true;
    
    // Copy recent clips to accumulated clips for processing
    accumulated_clips = recent_clips.slice(); // Make a copy
    log("Copied", recent_clips.length, "recent clips to accumulated clips");
    log("After: expecting_dump =", expecting_dump, "- accumulated_clips =", accumulated_clips.length, "items");
    
    // Don't output anything - the collection dump is triggered by the direct message path
    log("Ready to receive collection dump data");
    
    // Set a short timeout to process the recent clips
    if (dump_timeout) {
        dump_timeout.cancel();
    }
    dump_timeout = new Task(function() {
        log("Dump timeout reached - processing accumulated clips");
        if (expecting_dump) {
            process_accumulated_clips();
        }
    }, this);
    dump_timeout.schedule(100); // Short timeout since we already have the data
}

// Handle list data from inlet 1 (collection output)
function list() {
    log("List received on inlet", inlet, "- expecting_dump:", expecting_dump, "- dump_just_triggered:", dump_just_triggered, "- data:", arrayfromargs(arguments));
    
    if (inlet == 1) {
        // Always store recent collection outputs
        var received_data = [];
        for (var i = 0; i < arguments.length; i++) {
            received_data.push(arguments[i]);
        }
          // Check if this clip is already in recent_clips to avoid duplicates
        var data_string = received_data.join(",");
        var already_exists = false;
        
        // Only check for duplicates if we're NOT expecting a dump
        if (!expecting_dump) {
            for (var j = 0; j < recent_clips.length; j += 3) {
                if (j + 2 < recent_clips.length) {
                    var existing_string = recent_clips[j] + "," + recent_clips[j+1] + "," + recent_clips[j+2];
                    if (existing_string === data_string) {
                        already_exists = true;
                        break;
                    }
                }
            }
        }
        
        if (!already_exists || expecting_dump) {
            // Add to recent clips (unless it's a duplicate and we're not expecting dump)
            if (!already_exists) {
                for (var k = 0; k < received_data.length; k++) {
                    recent_clips.push(received_data[k]);
                }
                log("Added to recent clips:", received_data.join(" "), "- Total recent:", recent_clips.length / 3, "clips");
            } else {
                log("Duplicate clip ignored:", received_data.join(" "));
            }
        }
          if (expecting_dump) {
            // Always accumulate all clips during dump, regardless of duplicates
            for (var m = 0; m < received_data.length; m++) {
                accumulated_clips.push(received_data[m]);
            }
            log("Added to accumulated clips during dump:", received_data.join(" "), "- Total accumulated:", accumulated_clips.length, "items");
              // If this is the first data after dump was triggered, set a longer timeout
            if (dump_just_triggered) {
                dump_just_triggered = false;
                // Cancel any existing timeout
                if (dump_timeout) {
                    dump_timeout.cancel();
                }
                // Set a longer timeout to allow all clips to accumulate
                dump_timeout = new Task(function() {
                    log("Timeout reached - processing accumulated clips");
                    if (expecting_dump) {
                        process_accumulated_clips();
                    }
                }, this);
                dump_timeout.schedule(500); // Longer timeout to accumulate all clips
                log("Set longer timeout for accumulating all clips from dump");
            } else {
                // Extend the timeout since more data is coming
                if (dump_timeout) {
                    dump_timeout.cancel();
                    dump_timeout = new Task(function() {
                        log("Extended timeout reached - processing accumulated clips");
                        if (expecting_dump) {
                            process_accumulated_clips();
                        }
                    }, this);
                    dump_timeout.schedule(200); // Shorter extension timeout                    log("Extended timeout to wait for more clips");
                }
            }
        } else {
            log("Not expecting dump - only stored in recent clips");
        }
    } else {
        log("Received list on inlet 0 - ignoring");
    }
}

function process_accumulated_clips() {
    expecting_dump = false;
    dump_just_triggered = false;
    
    // Cancel any pending timeout
    if (dump_timeout) {
        dump_timeout.cancel();
        dump_timeout = null;
    }
    
    log("Processing accumulated clips:", accumulated_clips.length, "total items");
    
    if (accumulated_clips.length === 0) {
        log("Empty queue - nothing to launch");
        outlet(0, "empty");
        launch_when_dumped = false; // Reset flag
        return;
    }
    
    // Check if we should launch immediately
    if (launch_when_dumped) {
        log("Launch flag set - launching all clips immediately");
        launch_when_dumped = false; // Reset flag
        
        // Launch all clips directly from accumulated data
        for (var i = 0; i < accumulated_clips.length; i += 3) {
            if (i + 2 < accumulated_clips.length) {
                var velocity = accumulated_clips[i];      
                var track = accumulated_clips[i + 1];    
                var scene = accumulated_clips[i + 2];    
                
                log("Launching clip: velocity=" + velocity + " track=" + track + " scene=" + scene);
                if (typeof track === 'number' && typeof scene === 'number' && 
                    track >= 0 && scene >= 0) {
                    launch_single_queued_clip(track, scene);
                }
            }
        }
        
        // Clear data after launching
        accumulated_clips = [];
        recent_clips = [];
        return;
    }
    
    // Normal queuing process
    var launched_count = 0;
    
    // Process in groups of 3: velocity, track, scene (collection outputs index separately)
    for (var i = 0; i < accumulated_clips.length; i += 3) {
        if (i + 2 < accumulated_clips.length) {
            var velocity = accumulated_clips[i];      
            var track = accumulated_clips[i + 1];    
            var scene = accumulated_clips[i + 2];    
            
            log("Processing clip: velocity=" + velocity + " track=" + track + " scene=" + scene);
              if (typeof track === 'number' && typeof scene === 'number' && 
                track >= 0 && scene >= 0) {
                if (launch_clip(track, scene)) {
                    launched_count++;
                    // Store the queued clip in the collection for global operations
                    outlet(0, "store_queued_clip", track, scene);
                }
            } else {
                log("Invalid clip data - track:", track, "scene:", scene);
            }
        }
    }      log("Successfully queued", launched_count, "clips");
    outlet(0, "queued", launched_count);
    
    // Clear both accumulated and recent data after queuing
    accumulated_clips = [];
    recent_clips = [];
    log("Cleared both accumulated and recent clips after queuing");
}

// Handle anything - catch-all for inlet 0 
function anything() {
    if (inlet == 0) {
        var message_name = messagename;
        
        // Ignore signal messages to prevent audio blocking
        if (message_name === "signal") {
            return;
        }if (message_name === "dump") {
            dump();        } else if (message_name === "clear") {
            log("Clear command received - clearing all clips and state");
            log("Before clear: recent_clips length =", recent_clips.length, "accumulated_clips length =", accumulated_clips.length);
            recent_clips = [];
            accumulated_clips = [];
            expecting_dump = false;
            dump_just_triggered = false;
            launch_when_dumped = false; // Reset launch flag
            
            // Cancel any pending timeout
            if (dump_timeout) {
                dump_timeout.cancel();
                dump_timeout = null;
            }
            
            log("After clear: recent_clips length =", recent_clips.length, "accumulated_clips length =", accumulated_clips.length);
        } else if (message_name === "launch_all_queued") {
            log("*** MESSAGE HANDLER DEBUG: NORMAL LAUNCH MESSAGE RECEIVED ***");
            log("*** MESSAGE HANDLER DEBUG: messagename:", messagename);
            log("*** MESSAGE HANDLER DEBUG: inlet:", inlet); 
            log("*** MESSAGE HANDLER DEBUG: arguments.length:", arguments.length);
            log("*** MESSAGE HANDLER DEBUG: About to call launch_all_queued() ***");
            launch_all_queued();
            log("*** MESSAGE HANDLER DEBUG: launch_all_queued() completed ***");
        } else if (message_name === "launch_all_queued_immediate") {
            log("*** MESSAGE HANDLER DEBUG: IMMEDIATE LAUNCH MESSAGE RECEIVED ***");
            log("*** MESSAGE HANDLER DEBUG: messagename:", messagename);
            log("*** MESSAGE HANDLER DEBUG: inlet:", inlet);
            log("*** MESSAGE HANDLER DEBUG: arguments.length:", arguments.length);
            log("*** MESSAGE HANDLER DEBUG: About to call launch_all_queued_immediate() ***");
            launch_all_queued_immediate();
            log("*** MESSAGE HANDLER DEBUG: launch_all_queued_immediate() completed ***");
        } else if (message_name === "clear_all_queued") {
            clear_all_queued();
        } else if (message_name === "launch_queued_clips") {
            launch_queued_clips.apply(this, arrayfromargs(arguments));        } else if (message_name === "clear_queued_clips") {
            clear_queued_clips.apply(this, arrayfromargs(arguments));        } else if (message_name === "test_queuing") {
            log("*** TEST_QUEUING MESSAGE RECEIVED ***");
            if (arguments.length >= 2) {
                log("Calling test_clip_queuing with args:", arguments[0], arguments[1]);
                test_clip_queuing(arguments[0], arguments[1]);
            } else {
                log("test_queuing requires track and scene arguments");
            }        } else if (message_name === "inspect_clip") {
            log("*** INSPECT_CLIP MESSAGE RECEIVED ***");
            if (arguments.length >= 2) {
                log("Calling inspect_clip_state with args:", arguments[0], arguments[1]);
                inspect_clip_state(arguments[0], arguments[1]);
            } else {
                log("inspect_clip requires track and scene arguments");
            }
        } else if (message_name === "setup_quantization") {
            log("*** SETUP_QUANTIZATION MESSAGE RECEIVED ***");
            ensure_quantization_for_queuing();
        } else {
            log("Received message on inlet 0:", message_name, arrayfromargs(arguments));
        }
    } else {
        log("Ignoring message on inlet 1:", messagename);
    }
}

// Handle bang 
function bang() {
    log("Bang received on inlet", inlet, "- expecting_dump:", expecting_dump);
    
    if (inlet == 2 && expecting_dump) {
        // Bang from inlet 2 means collection dump is complete - process accumulated clips
        log("Dump complete bang received - processing accumulated clips");
        
        // Cancel timeout since we got the proper completion signal
        if (dump_timeout) {
            dump_timeout.cancel();
            dump_timeout = null;
        }
        
        process_accumulated_clips();
    } else if (inlet == 2) {
        log("Ignoring bang on inlet 2 - not expecting dump");
    } else {
        log("Bang received on inlet", inlet, "- no action");
        outlet(0, "error", "no_queue_data");
    }
}

// Launch all queued clips globally - this releases all set_fire_button_state clips
function launch_all_queued() {
    log("*** LAUNCH ALL QUEUED DEBUG: Entry point - launch_all_queued() called ***");
    log("*** LAUNCH ALL QUEUED DEBUG: messagename:", messagename);
    log("*** LAUNCH ALL QUEUED DEBUG: inlet:", inlet);
    log("*** LAUNCH ALL QUEUED DEBUG: recent_clips.length =", recent_clips.length);
    log("*** LAUNCH ALL QUEUED DEBUG: arguments length:", arguments.length);
    
    // Check if we have clips to launch
    if (recent_clips.length === 0) {
        log("*** LAUNCH ALL QUEUED DEBUG: No clips queued - exiting ***");
        outlet(0, "empty");
        return;
    }
    
    log("*** LAUNCH ALL QUEUED DEBUG: Processing", recent_clips.length / 3, "clips ***");
    
    // Analyze clips to see if we can use scene launching for perfect sync
    var clips_by_scene = {};
    var all_clips_prepared = [];
    
    // First pass: Group clips by scene and prepare them
    for (var i = 0; i < recent_clips.length; i += 3) {
        if (i + 2 < recent_clips.length) {
            var velocity = recent_clips[i];      
            var track = recent_clips[i + 1];    
            var scene = recent_clips[i + 2];    
            
            if (typeof track === 'number' && typeof scene === 'number' && 
                track >= 0 && scene >= 0) {
                
                // Group by scene for potential scene launching
                if (!clips_by_scene[scene]) {
                    clips_by_scene[scene] = [];
                }
                clips_by_scene[scene].push({track: track, scene: scene});
                
                // Also prepare individual clips as backup
                var clipInfo = prepare_clip_for_launch(track, scene);
                if (clipInfo) {
                    all_clips_prepared.push(clipInfo);
                    log("*** LAUNCH ALL QUEUED DEBUG: Prepared clip for launch: track", track, "scene", scene, "***");
                }
            }
        }
    }
    
    // Check if all clips are in the same scene - DISABLED scene launching due to launching unwanted clips
    var scene_numbers = Object.keys(clips_by_scene);
    if (scene_numbers.length === 1) {
        var target_scene = parseInt(scene_numbers[0]);
        log("*** LAUNCH ALL QUEUED DEBUG: ALL CLIPS IN SAME SCENE", target_scene, "- BUT USING INDIVIDUAL LAUNCHES TO AVOID FIRING UNWANTED CLIPS ***");
        
        // SCENE LAUNCHING DISABLED: It fires ALL clips in the scene, not just queued ones
        // We'll always use individual launches for precise control
    } else {
        log("*** LAUNCH ALL QUEUED DEBUG: CLIPS SPAN MULTIPLE SCENES:", scene_numbers.join(", "), "- USING INDIVIDUAL LAUNCHES ***");
    }
    
    // Fallback: Use deferlow for individual clip launching
    log("*** LAUNCH ALL QUEUED DEBUG: USING DEFERLOW FOR INDIVIDUAL CLIP SYNCHRONIZATION ***");
    var defer_task = new Task(function() {
        log("*** LAUNCH ALL QUEUED DEBUG: DEFERLOW TASK STARTING - launching", all_clips_prepared.length, "clips ***");
        
        // Launch all clips in the tightest possible loop for maximum sync
        for (var j = 0; j < all_clips_prepared.length; j++) {
            var clip = all_clips_prepared[j];
            try {
                log("*** LAUNCH ALL QUEUED DEBUG: Firing clip", j+1, "of", all_clips_prepared.length, "- track", clip.track, "scene", clip.scene, "***");
                clip.liveObject.call("fire");
                log("*** LAUNCH ALL QUEUED DEBUG: Successfully fired clip ***");
            } catch (fire_error) {
                log("*** LAUNCH ALL QUEUED DEBUG: ERROR firing clip at track", clip.track, "scene", clip.scene, ":", fire_error.toString(), "***");
            }
        }
        log("*** LAUNCH ALL QUEUED DEBUG: ALL CLIPS FIRED - DEFERLOW TASK COMPLETE ***");
        
        // Clear recent clips after launching
        recent_clips = [];
        log("*** LAUNCH ALL QUEUED DEBUG: Cleared recent clips after launching ***");
    }, this);
    
    // Use deferlow for maximum synchronization - no delay, just deferred execution
    defer_task.schedule(); // No arguments = immediate deferlow scheduling
    log("*** LAUNCH ALL QUEUED DEBUG: Scheduled deferlow task - exiting main function ***");
}

// Launch all queued clips with IMMEDIATE timing (Session + Play when transport stopped - internal)
function launch_all_queued_immediate_internal() {
    // Check if we have clips to launch
    if (recent_clips.length === 0) {
        log("No clips queued - nothing to launch immediately");
        outlet(0, "empty");
        return;
    }
    
    log("*** SESSION + PLAY WITH TRANSPORT STOPPED - IMMEDIATE LAUNCH ***");
    log("*** USING SET_FIRE_BUTTON_STATE FOR PERFECT SYNC ***");
    
    // Transport is stopped - prepare clips for synchronized immediate launch
    var clips_for_immediate = [];
    
    for (var i = 0; i < recent_clips.length; i += 3) {
        if (i + 2 < recent_clips.length) {
            var velocity = recent_clips[i];      
            var track = recent_clips[i + 1];    
            var scene = recent_clips[i + 2];    
            
            if (typeof track === 'number' && typeof scene === 'number' && 
                track >= 0 && scene >= 0) {
                
                var clipInfo = prepare_clip_for_immediate_launch(track, scene);
                if (clipInfo) {
                    clips_for_immediate.push(clipInfo);
                    log("Prepared clip for immediate launch: track", track, "scene", scene);
                }
            }
        }
    }
    
    log("*** USING HYBRID SYNC: set_fire_button_state + direct fire fallback ***");
    
    // STEP 1: Set all clips to fire state and pre-collect both ClipSlot and Clip objects
    var clip_slots = [];
    var clip_objects = [];
    var track_scene_pairs = [];
    
    for (var j = 0; j < clips_for_immediate.length; j++) {
        var clipInfo = clips_for_immediate[j];
        try {
            // Use set_fire_button_state(1) on ClipSlot for perfect sync
            clipInfo.liveObject.call("set_fire_button_state", 1);
            log("*** HYBRID SYNC: set_fire_button_state(1) - track", clipInfo.track, "scene", clipInfo.scene, "***");
            
            // Pre-collect both ClipSlot and Clip objects for hybrid approach
            clip_slots.push(clipInfo.liveObject);
            clip_objects.push(clipInfo.clipObject);
            track_scene_pairs.push({track: clipInfo.track, scene: clipInfo.scene});
            
        } catch (fire_error) {
            log("ERROR in set_fire_button_state(1):", fire_error.toString());
        }
    }
    
    log("*** PREPARED", clip_slots.length, "CLIPS FOR HYBRID SYNC RELEASE ***");
    
    // STEP 2: Use immediate deferlow with hybrid synchronization approach
    var defer_task = new Task(function() {
        log("*** HYBRID SYNC: DUAL-METHOD RELEASE STARTING ***");
        
        // METHOD 1: Try set_fire_button_state(0) approach first
        for (var k = 0; k < clip_slots.length; k++) {
            try {
                clip_slots[k].call("set_fire_button_state", 0);
            } catch (release_error) {
                log("ERROR in set_fire_button_state(0) for clip", k, "- trying direct fire");
                // FALLBACK: If set_fire_button_state fails, use direct fire
                try {
                    clip_objects[k].call("fire");
                } catch (fire_error) {
                    log("ERROR in direct fire fallback for clip", k, ":", fire_error.toString());
                }
            }
        }
        
        log("*** HYBRID RELEASE COMPLETE - LOGGED AFTER RELEASE ***");
        for (var m = 0; m < track_scene_pairs.length; m++) {
            log("*** RELEASED: track", track_scene_pairs[m].track, "scene", track_scene_pairs[m].scene, "***");
        }
        
        log("*** IMMEDIATE LAUNCH SEQUENCE COMPLETE - HYBRID SYNC ACHIEVED ***");
        
        // Clear recent clips after launching
        recent_clips = [];
        log("Cleared recent clips after immediate launching");
    }, this);
    
    // Use immediate deferlow for single-frame synchronization
    defer_task.schedule(); // No delay argument = immediate scheduling
}

// Launch all queued clips with IMMEDIATE timing (Session + Play detection - simple wrapper)
function launch_all_queued_immediate() {
    log("*** SESSION + PLAY DEBUG: Entry point - launch_all_queued_immediate() called ***");
    log("*** SESSION + PLAY DEBUG: messagename:", messagename);
    log("*** SESSION + PLAY DEBUG: inlet:", inlet);
    log("*** SESSION + PLAY DEBUG: recent_clips.length =", recent_clips.length);
    log("*** SESSION + PLAY DEBUG: arguments length:", arguments.length);
    
    // Direct check at the beginning to avoid execution context issues
    var transportPlaying = false;
    try {
        var liveSet = new LiveAPI("live_set");
        var isPlaying = liveSet.get("is_playing");
        transportPlaying = isPlaying && isPlaying[0] === 1;
        log("*** SESSION + PLAY DEBUG: transport is_playing API result =", isPlaying);
        log("*** SESSION + PLAY DEBUG: transportPlaying boolean =", transportPlaying);
    } catch (transport_error) {
        log("*** SESSION + PLAY DEBUG: transport check ERROR:", transport_error.toString());
    }
    
    if (transportPlaying) {
        log("*** SESSION + PLAY DEBUG: Transport playing - redirecting to normal launch ***");
        log("*** SESSION + PLAY DEBUG: About to call launch_all_queued() ***");
        launch_all_queued();
        log("*** SESSION + PLAY DEBUG: launch_all_queued() completed - returning ***");
        return;
    }
    
    log("*** SESSION + PLAY DEBUG: Transport stopped - using immediate launch ***");
    launch_all_queued_immediate_internal();
}

// Helper function to prepare a clip for synchronized launch
function prepare_clip_for_launch(track, scene) {
    try {
        var path = "live_set tracks " + track + " clip_slots " + scene;
        var liveObject = new LiveAPI(path);
        
        if (!liveObject || liveObject.id == 0) {
            log("Could not create LiveAPI for track", track, "scene", scene);
            return null;
        }
        
        // Check if clip exists
        var has_clip = liveObject.get("has_clip");
        if (!has_clip || has_clip[0] != 1) {
            log("No clip found at track", track, "scene", scene);
            return null;
        }
        
        // DO NOT modify quantization - this causes transport restarts during playback
        var clipPath = path + " clip";
        var clipObject = new LiveAPI(clipPath);
        if (clipObject && clipObject.id != 0) {
            try {
                var clip_launch_quant = clipObject.get("launch_quantization");
                log("*** NO QUANTIZATION CHANGES for track", track, "scene", scene, "- using ClipSlot.fire() with existing settings ***");
                // Let the clip use its existing quantization settings
            } catch (quant_error) {
                log("Could not read quantization for track", track, "scene", scene, ":", quant_error.toString());
            }
        }
        
        return {
            track: track,
            scene: scene,
            liveObject: liveObject
        };
        
    } catch (error) {
        log("Error preparing clip at track", track, "scene", scene, ":", error.toString());
        return null;
    }
}

// Prepare a clip for immediate launch WITHOUT modifying quantization
function prepare_clip_for_immediate_launch(track_index, scene_index) {
    try {
        var trackPath = "live_set tracks " + track_index;
        var track = new LiveAPI(trackPath);
        
        if (!track || !track.id) {
            log("ERROR: Could not get track for immediate launch at index", track_index);
            return null;
        }
        
        var clipSlotPath = trackPath + " clip_slots " + scene_index;
        var clipSlot = new LiveAPI(clipSlotPath);
        
        if (!clipSlot || !clipSlot.id) {
            log("ERROR: Could not get clip_slot for immediate launch at track", track_index, "scene", scene_index);
            return null;
        }
        
        // Check if clip slot has a clip
        var hasClip = clipSlot.get("has_clip");
        if (!hasClip || hasClip[0] !== 1) {
            log("WARNING: No clip in slot for immediate launch at track", track_index, "scene", scene_index);
            return null;
        }
        
        var clipPath = clipSlotPath + " clip";
        var clip = new LiveAPI(clipPath);
        
        if (!clip || !clip.id) {
            log("ERROR: Could not get clip for immediate launch at track", track_index, "scene", scene_index);
            return null;
        }
        
        // DON'T MODIFY QUANTIZATION - just return ClipSlot for firing
        log("*** NO QUANTIZATION CHANGES for immediate launch: track", track_index, "scene", scene_index, "***");
        
        // Return ClipSlot for immediate firing
        return {
            liveObject: clipSlot,  // Use ClipSlot for firing
            clipObject: clip,      // Keep clip reference 
            track: track_index,
            scene: scene_index
        };
        
    } catch (error) {
        log("ERROR preparing clip for immediate launch at track", track_index, "scene", scene_index, ":", error.toString());
        return null;
    }
}

// Clear all queued clips globally - this cancels all set_fire_button_state clips
function clear_all_queued() {
    log("Global clear command received - clearing all queued clips");
    
    // To clear all queued clips, we set fire_button_state to 0 for all queued clips
    // But first we need to get the list of queued clips from the collection
    outlet(0, "get_queued_clips_for_clear");
}

// Handle launch of specific queued clips (called after getting queued clips list)
function launch_queued_clips() {
    log("launch_queued_clips called with arguments:", arrayfromargs(arguments));
    
    var args = arrayfromargs(arguments);
    
    // Handle the data format: velocity, track, scene (in groups of 3)
    for (var i = 0; i < args.length; i += 3) {
        if (i + 2 < args.length && typeof args[i] === 'number' && typeof args[i + 1] === 'number' && typeof args[i + 2] === 'number') {
            var velocity = args[i];     // First value is velocity (ignore)
            var track = args[i + 1];    // Second value is track
            var scene = args[i + 2];    // Third value is scene
            
            log("Launching clip at track", track, "scene", scene, "(velocity", velocity, ")");
            launch_single_queued_clip(track, scene);
        } else {
            log("Insufficient or invalid arguments for clip launch at index", i, "- args:", args[i], args[i + 1], args[i + 2]);
        }
    }
}

// Launch a single clip with specific track and scene
function launch_single_queued_clip(track, scene) {
    log("Launching single queued clip at track", track, "scene", scene);
    
    try {
        // Check current quantization to determine launch strategy
        var sessionObject = new LiveAPI("live_set");
        var current_quantization = 0;
        if (sessionObject && sessionObject.id != 0) {
            var session_quant = sessionObject.get("clip_trigger_quantization");
            if (session_quant && session_quant.length > 0) {
                current_quantization = session_quant[0];
            }
        }
        
        var path = "live_set tracks " + track + " clip_slots " + scene;
        log("Creating LiveAPI for global launch with path:", path);
        var liveObject = new LiveAPI(path);
        if (liveObject && liveObject.id != 0) {
            log("LiveAPI created for global launch, ID:", liveObject.id);
            
            // Check if clip exists
            var has_clip = liveObject.get("has_clip");
            if (!has_clip || has_clip[0] != 1) {
                log("No clip found at track", track, "scene", scene);
                return;
            }
            
            // Check current state before launching
            var current_triggered = liveObject.get("is_triggered");
            log("Current is_triggered before launch:", current_triggered);
            
            // Check individual clip quantization settings
            var clipPath = path + " clip";
            var clipObject = new LiveAPI(clipPath);
            if (clipObject && clipObject.id != 0) {
                try {
                    var clip_launch_quant = clipObject.get("launch_quantization");
                    log("Individual clip launch_quantization:", clip_launch_quant);
                    
                    // For perfect sync, temporarily set clip quantization to immediate
                    if (clip_launch_quant && clip_launch_quant[0] != 0) {
                        log("*** OVERRIDING CLIP QUANTIZATION FOR PERFECT SYNC ***");
                        log("*** Changing from", clip_launch_quant[0], "to 0 (Immediate) ***");
                        clipObject.set("launch_quantization", 0);
                        
                        // Verify the change
                        var new_quant = clipObject.get("launch_quantization");
                        log("New clip launch_quantization:", new_quant);
                    }
                } catch (quant_error) {
                    log("Could not access clip quantization:", quant_error.toString());
                }
            }
            
            if (current_quantization == 0) {
                // IMMEDIATE QUANTIZATION: Use fire() for instant synchronized launch
                log("*** IMMEDIATE MODE: Using fire() for instant synchronized launch at track", track, "slot", scene);
                try {
                    liveObject.call("fire");
                    log("*** IMMEDIATE fire() completed for perfect sync ***");
                } catch (fire_error) {
                    log("ERROR in immediate fire():", fire_error.toString());
                }
            } else {
                // QUANTIZED MODE: Use set_fire_button_state 0 to release queued clips
                log("*** QUANTIZED MODE: Using set_fire_button_state 0 to release clip at track", track, "slot", scene);
                try {
                    liveObject.call("set_fire_button_state", 0);
                    log("set_fire_button_state 0 completed");
                    
                    // Verify the state was changed
                    var task = new Task(function() {
                        var new_triggered = liveObject.get("is_triggered");
                        log("New is_triggered after set_fire_button_state 0:", new_triggered);
                        
                        // If set_fire_button_state 0 didn't launch, try fire() as backup
                        if (new_triggered && new_triggered[0] == 1) {
                            log("Clip still triggered after set_fire_button_state 0, trying fire() as backup...");
                            try {
                                liveObject.call("fire");
                                log("Backup fire() called to launch queued clip");
                            } catch (fire_error) {
                                log("Error in backup fire():", fire_error.toString());
                            }
                        } else {
                            log("SUCCESS: Clip released and launched via set_fire_button_state 0");
                        }
                    });
                    task.schedule(50);
                } catch (release_error) {
                    log("Error calling set_fire_button_state 0:", release_error.toString());
                    
                    // Fallback to fire() if set_fire_button_state 0 fails
                    log("Fallback: trying fire() to launch queued clip");
                    try {
                        liveObject.call("fire");
                        log("Fallback fire() called");
                    } catch (fire_error) {
                        log("Error in fallback fire():", fire_error.toString());
                    }
                }
            }
            
            log("Attempted to launch queued clip at track", track, "slot", scene);
        } else {
            log("Could not access clip slot for global launch at track", track, "slot", scene, "- LiveAPI ID:", liveObject ? liveObject.id : "null");
        }
    } catch (error) {
        log("Error launching queued clip at track", track, "slot", scene, ":", error.toString());
    }
}

// Handle clearing of specific queued clips (called after getting queued clips list)
function clear_queued_clips() {
    if (arguments.length >= 2) {
        var track = arguments[0];
        var scene = arguments[1];
        
        try {
            var liveObject = new LiveAPI("live_set tracks " + track + " clip_slots " + scene);
            if (liveObject && liveObject.id != 0) {
                // Check if clip is triggered (queued)
                var is_triggered = liveObject.get("is_triggered");
                if (is_triggered && is_triggered[0] == 1) {
                    // Set fire_button_state to 0 to clear the queued clip
                    liveObject.call("set_fire_button_state", 0);
                    log("Cleared queued clip at track", track, "slot", scene);
                } else {
                    log("Clip at track", track, "slot", scene, "was not triggered - nothing to clear");
                }
            }
        } catch (error) {
            log("Error clearing clip at track", track, "slot", scene, ":", error.toString());
        }
    }
}

// Test function to try different approaches to queuing
function test_clip_queuing(track_num, clip_slot_num) {
    log("=== COMPREHENSIVE CLIP QUEUING TEST ===");
    log("Testing track", track_num, "clip slot", clip_slot_num);
    
    try {
        // First, let's examine the current session and available methods
        var session_path = "live_set";
        var sessionObject = new LiveAPI(session_path);
        if (sessionObject && sessionObject.id != 0) {
            var tempo = sessionObject.get("tempo");
            log("Session tempo:", tempo);
            var is_playing = sessionObject.get("is_playing");
            log("Session is_playing:", is_playing);
              // Check quantization settings - this affects queuing behavior significantly
            var clip_trigger_quantization = sessionObject.get("clip_trigger_quantization");
            log("Session clip_trigger_quantization:", clip_trigger_quantization);
            
            if (clip_trigger_quantization && clip_trigger_quantization[0] == 0) {
                log("*** QUANTIZATION IS 0 (IMMEDIATE) - set_fire_button_state will launch instantly! ***");
                log("*** For sync with immediate quantization, clips must be launched together with fire() ***");
            } else {
                log("*** QUANTIZATION IS NOT IMMEDIATE - set_fire_button_state should queue properly ***");
            }
        }
        
        // Test ClipSlot approach with comprehensive property checking
        var slot_path = "live_set tracks " + track_num + " clip_slots " + clip_slot_num;
        log("Testing ClipSlot path:", slot_path);
        var slotObject = new LiveAPI(slot_path);
        
        if (slotObject && slotObject.id != 0) {
            log("ClipSlot LiveAPI created successfully, ID:", slotObject.id);
            
            // Check all relevant properties before attempting to queue
            var has_clip = slotObject.get("has_clip");
            log("ClipSlot has_clip:", has_clip);
            
            if (has_clip && has_clip[0] == 1) {
                // Check all the state properties first
                var initial_triggered = slotObject.get("is_triggered");
                log("Initial is_triggered:", initial_triggered);
                
                var will_record = slotObject.get("will_record_on_start");
                log("will_record_on_start:", will_record);
                
                // NEW: Try using fire() on the ClipSlot first to see if it queues properly
                log("=== TESTING ClipSlot fire() method ===");
                try {
                    slotObject.call("fire");
                    log("ClipSlot fire() call completed without error");
                    
                    var task_fire = new Task(function() {
                        var triggered_after_fire = slotObject.get("is_triggered");
                        log("is_triggered after ClipSlot fire():", triggered_after_fire);
                        
                        if (triggered_after_fire && triggered_after_fire[0] == 1) {
                            log("SUCCESS: ClipSlot fire() put clip in triggered/pulsing state!");
                            
                            // Now test clearing it with set_fire_button_state 0
                            log("Testing clear with set_fire_button_state 0...");
                            slotObject.call("set_fire_button_state", 0);
                            
                            var task_clear = new Task(function() {
                                var triggered_after_clear = slotObject.get("is_triggered");
                                log("is_triggered after set_fire_button_state 0:", triggered_after_clear);
                                if (triggered_after_clear && triggered_after_clear[0] == 0) {
                                    log("SUCCESS: set_fire_button_state 0 cleared the triggered state!");
                                } else {
                                    log("INFO: set_fire_button_state 0 called but clip still triggered");
                                }
                            });
                            task_clear.schedule(100);
                            
                        } else {
                            log("INFO: ClipSlot fire() called but clip not in triggered state");
                            
                            // If fire() didn't work, try the original set_fire_button_state approach
                            log("=== FALLBACK: Testing set_fire_button_state 1 ===");
                            slotObject.call("set_fire_button_state", 1);
                            
                            var task_set = new Task(function() {
                                var triggered_after_set = slotObject.get("is_triggered");
                                log("is_triggered after set_fire_button_state 1:", triggered_after_set);
                            });
                            task_set.schedule(100);
                        }
                    });
                    task_fire.schedule(100);
                    
                } catch (fire_error) {
                    log("Error calling fire() on ClipSlot:", fire_error.toString());
                }
                  // Also test the actual Clip object path
                var clip_path = "live_set tracks " + track_num + " clip_slots " + clip_slot_num + " clip";
                log("Testing Clip object path:", clip_path);
                var clipObject = new LiveAPI(clip_path);
                
                if (clipObject && clipObject.id != 0) {
                    log("Clip LiveAPI created, ID:", clipObject.id);
                    
                    // Check clip properties
                    var clip_name = clipObject.get("name");
                    log("Clip name:", clip_name);
                    
                    var clip_length = clipObject.get("length");
                    log("Clip length:", clip_length);
                    
                    var clip_is_triggered = clipObject.get("is_triggered");
                    log("Clip is_triggered (initial):", clip_is_triggered);
                    
                    // Check if the clip has a launch_quantization property
                    try {
                        var launch_quant = clipObject.get("launch_quantization");
                        log("Clip launch_quantization:", launch_quant);
                        
                        // CRITICAL: Try to set clip quantization if it's 0
                        if (launch_quant && launch_quant[0] == 0) {
                            log("*** CLIP QUANTIZATION IS 0 - TRYING TO SET TO 1 BAR ***");
                            try {
                                clipObject.set("launch_quantization", 4); // 4 = 1 Bar
                                log("Set clip launch_quantization to 4 (1 Bar)");
                                
                                var new_clip_quant = clipObject.get("launch_quantization");
                                log("New clip launch_quantization:", new_clip_quant);
                                
                                // Now try queuing again with quantization enabled
                                log("*** RETRYING QUEUING WITH QUANTIZATION ENABLED ***");
                                slotObject.call("fire");
                                log("Retried ClipSlot fire() with quantization");
                                
                                var task_retry = new Task(function() {
                                    var triggered_retry = slotObject.get("is_triggered");
                                    log("is_triggered after retry with quantization:", triggered_retry);
                                    
                                    if (triggered_retry && triggered_retry[0] == 1) {
                                        log("SUCCESS: Clip is now queued with quantization enabled!");
                                        
                                        // Test releasing with set_fire_button_state 0
                                        setTimeout(function() {
                                            log("Testing release after 2 seconds...");
                                            slotObject.call("set_fire_button_state", 0);
                                        }, 2000);
                                    } else {
                                        log("Still no queuing even with quantization enabled");
                                    }
                                });
                                task_retry.schedule(100);
                                
                            } catch (clip_quant_error) {
                                log("Error setting clip quantization:", clip_quant_error.toString());
                            }
                        }
                    } catch (quant_error) {
                        log("Could not get launch_quantization:", quant_error.toString());
                    }
                } else {
                    log("Could not access Clip object at path:", clip_path);
                }
                
            } else {
                log("No clip found in slot - has_clip result:", has_clip);
            }
        } else {
            log("Could not access ClipSlot at path:", slot_path, "- ID:", slotObject ? slotObject.id : "null");
        }
        
    } catch (error) {
        log("Error in comprehensive test:", error.toString());
    }
    
    log("=== END COMPREHENSIVE TEST ===");
}

// Add an inspection function to just read clip states without modifying
function inspect_clip_state(track_num, clip_slot_num) {
    log("=== CLIP STATE INSPECTION ===");
    log("Inspecting track", track_num, "clip slot", clip_slot_num);
    
    try {
        var slot_path = "live_set tracks " + track_num + " clip_slots " + clip_slot_num;
        var slotObject = new LiveAPI(slot_path);
        
        if (slotObject && slotObject.id != 0) {
            log("ClipSlot LiveAPI ID:", slotObject.id);
            
            var has_clip = slotObject.get("has_clip");
            log("has_clip:", has_clip);
              if (has_clip && has_clip[0] == 1) {
                var is_triggered = slotObject.get("is_triggered");
                log("is_triggered:", is_triggered);
                
                var will_record = slotObject.get("will_record_on_start");
                log("will_record_on_start:", will_record);
                
                // Try accessing the clip object directly too
                var clip_path = slot_path + " clip";
                var clipObject = new LiveAPI(clip_path);
                if (clipObject && clipObject.id != 0) {
                    log("Clip LiveAPI ID:", clipObject.id);
                    
                    var clip_name = clipObject.get("name");
                    log("Clip name:", clip_name);
                    
                    var clip_is_triggered = clipObject.get("is_triggered");
                    log("Clip is_triggered:", clip_is_triggered);
                } else {
                    log("Could not access Clip object");
                }
            } else {
                log("No clip found in slot - has_clip:", has_clip);
            }
        } else {
            log("Could not access ClipSlot - ID:", slotObject ? slotObject.id : "null");
        }
    } catch (error) {
        log("Error in clip inspection:", error.toString());
    }
    
    log("=== END INSPECTION ===");
}

// ClipLauncher JavaScript for Push 2 - v6.7
// SESSION + PLAY SYSTEM: PRODUCTION READY
// Feature: Synchronized clip launching with Session button + Play button controls
// Session + Play: Context-aware launching (immediate when stopped, quantized when playing)
// Hybrid synchronization: set_fire_button_state + direct fire() fallback
// PAD LIGHT PRESERVATION: Show clip availability during Session mode (FIXED)

// Global variables
var api_version = "6.7";

// Debugging output
function debug_log(message) {
    post("DEBUG: " + message);
}

// PAD LIGHT PRESERVATION SYSTEM
var session_mode_active = false;
var original_pad_states = {}; // Store original clip states for restoration

// LiveAPI objects for clip observation
var scene_count = 8;
var track_count = 8;

// Get current clip states for pad lighting
function get_current_clip_states() {
    debug_log("Getting current clip states for pad lighting");
    var clip_states = {};
    
    try {
        // Use live_set first to ensure we have a valid API connection
        var live_set = new LiveAPI("live_set");
        if (!live_set || live_set.id == 0) {
            debug_log("Could not access live_set");
            return clip_states;
        }
        
        for (var scene = 0; scene < scene_count; scene++) {
            for (var track = 0; track < track_count; track++) {
                try {
                    // CORRECTED PATH: should be tracks X clip_slots Y
                    var clip_slot_path = "live_set tracks " + track + " clip_slots " + scene;
                    var live_api = new LiveAPI(clip_slot_path);
                    
                    if (live_api && live_api.id != 0) {
                        var has_clip = live_api.get("has_clip");
                        var pad_note = 36 + (scene * 8) + track; // MIDI note for pad matrix
                        
                        if (has_clip && has_clip.length > 0) {
                            clip_states[pad_note] = has_clip[0];
                            debug_log("Track " + track + " scene " + scene + " pad " + pad_note + " has_clip: " + has_clip[0]);
                            
                            // Also check if clip is playing for different color
                            if (has_clip[0]) {
                                var clip_api = new LiveAPI(clip_slot_path + " clip");
                                if (clip_api && clip_api.id != 0) {
                                    var is_playing = clip_api.get("is_playing");
                                    
                                    if (is_playing && is_playing.length > 0) {
                                        clip_states[pad_note + "_playing"] = is_playing[0];
                                    } else {
                                        clip_states[pad_note + "_playing"] = 0;
                                    }
                                }
                            }
                        } else {
                            clip_states[pad_note] = 0;
                            clip_states[pad_note + "_playing"] = 0;
                        }
                    } else {
                        debug_log("Could not access clip slot at track " + track + " scene " + scene);
                    }
                } catch (inner_error) {
                    debug_log("Error accessing track " + track + " scene " + scene + ": " + inner_error.toString());
                    clip_states[36 + (scene * 8) + track] = 0;
                }
            }
        }
    } catch (e) {
        debug_log("Error getting clip states: " + e.toString());
    }
    
    debug_log("Found " + Object.keys(clip_states).length + " clip states");
    return clip_states;
}

// Update pad LEDs with current clip states
function update_pad_leds_for_session_mode() {
    debug_log("Updating pad LEDs for Session mode");
    
    var clip_states = get_current_clip_states();
    var total_clips = 0;
    
    for (var scene = 0; scene < scene_count; scene++) {
        for (var track = 0; track < track_count; track++) {
            var pad_note = 36 + (scene * 8) + track;
            var has_clip = clip_states[pad_note];
            var is_playing = clip_states[pad_note + "_playing"];
            
            if (has_clip) {
                total_clips++;
                var color_value = is_playing ? 127 : 126; // Red for playing, green for stopped
                
                // Send LED update command to Max patch
                outlet(0, "set_pad_led", scene, track, color_value);
                debug_log("Setting LED: scene " + scene + " track " + track + " color " + color_value);
            } else {
                // Turn off LED for empty slots
                outlet(0, "set_pad_led", scene, track, 0);
            }
        }
    }
    
    debug_log("Updated " + total_clips + " pad LEDs for Session mode");
}

// Session mode control functions
function enter_session_mode() {
    debug_log("Entering Session mode - preserving pad lights");
    session_mode_active = true;
    
    // Store original clip states
    debug_log("Getting current clip states...");
    original_pad_states = get_current_clip_states();
    
    // Send debug message to verify communication
    outlet(0, "led_debug", "session_enter");
    debug_log("Sent session_enter debug message to Max");
    
    // Send initial LED commands
    send_test_led_commands();
    
    // Start continuous LED updates to override Push 2's normal behavior
    if (typeof led_update_task !== 'undefined' && led_update_task) {
        led_update_task.cancel();
    }
    
    led_update_task = new Task(function() {
        if (session_mode_active) {
            send_test_led_commands();
            // Schedule next update - frequent updates to maintain control
            led_update_task.schedule(100); // Every 100ms
        }
    });
    led_update_task.schedule(100);
    
    debug_log("Session enter complete - started continuous LED updates");
}

function send_test_led_commands() {
    // Send LED commands using Live API send_value method  
    var clip_count = 0;
    for (var scene = 0; scene < 2; scene++) { // Test just first 2 scenes
        for (var track = 0; track < 2; track++) { // Test just first 2 tracks
            var has_clip = original_pad_states[36 + (scene * 8) + track];
            var is_playing = original_pad_states[36 + (scene * 8) + track + "_playing"];
            
            if (has_clip) {
                clip_count++;
                var led_color = is_playing ? 127 : 126; // Red for playing, green for stopped
                
                debug_log("Found clip at scene " + scene + " track " + track + " - sending LED command");
                outlet(0, "led_debug", "clip_found", scene, track, 36 + (scene * 8) + track);
                
                // Use Live API send_value method with individual parameters
                // Format: call send_value row column velocity
                outlet(0, "call", "send_value", scene, track, led_color);
                debug_log("LED control: scene " + scene + " track " + track + " color " + led_color);
            } else {
                // Turn off LED for empty slots
                outlet(0, "call", "send_value", scene, track, 0);
            }
        }
    }
}

function exit_session_mode() {
    debug_log("Exiting Session mode - restoring normal LED behavior");
    session_mode_active = false;
    
    // Cancel continuous LED updates
    if (typeof led_update_task !== 'undefined' && led_update_task) {
        led_update_task.cancel();
        led_update_task = null;
    }
    
    // Send debug message for exit
    outlet(0, "led_debug", "session_exit");
    debug_log("Sent session_exit debug message to Max");
    
    // Turn off all test LEDs using Live API send_value
    for (var scene = 0; scene < 2; scene++) {
        for (var track = 0; track < 2; track++) {
            outlet(0, "call", "send_value", scene, track, 0); // Turn off LED
        }
    }
    debug_log("Cleared test area LEDs");
    
    // Reset to allow normal LED behavior
    original_pad_states = {};
}
