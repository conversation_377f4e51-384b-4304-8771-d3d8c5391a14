{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 6, "revision": 5, "architecture": "x64"}, "classnamespace": "box", "rect": [100.0, 100.0, 600.0, 400.0], "bglocked": 0, "openinpresentation": 1, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "presentation_rect": [100.0, 100.0, 400.0, 300.0], "boxes": [{"box": {"maxclass": "comment", "text": "Push 2 RESET - Clear Stuck LEDs", "presentation_rect": [10.0, 10.0, 300.0, 20.0], "presentation": 1, "patching_rect": [30.0, 30.0, 300.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 14.0, "fontface": 1, "id": "obj-1"}}, {"box": {"maxclass": "live.path", "patching_rect": [30.0, 60.0, 200.0, 15.0], "numinlets": 1, "numoutlets": 1, "outlettype": [""], "id": "obj-2"}}, {"box": {"maxclass": "message", "text": "path control_surfaces 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 80.0, 140.0, 22.0], "numinlets": 2, "id": "obj-3"}}, {"box": {"maxclass": "live.object", "text": "live.object", "patching_rect": [30.0, 110.0, 60.0, 15.0], "numinlets": 1, "numoutlets": 1, "outlettype": [""], "id": "obj-4"}}, {"box": {"maxclass": "message", "text": "call grab_control Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 130.0, 180.0, 22.0], "numinlets": 2, "id": "obj-5"}}, {"box": {"maxclass": "button", "patching_rect": [30.0, 160.0, 20.0, 20.0], "presentation_rect": [20.0, 50.0, 60.0, 20.0], "presentation": 1, "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "id": "obj-6"}}, {"box": {"maxclass": "comment", "text": "1. Connect", "presentation_rect": [10.0, 50.0, 80.0, 20.0], "presentation": 1, "patching_rect": [10.0, 160.0, 80.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-7"}}, {"box": {"maxclass": "button", "patching_rect": [30.0, 200.0, 20.0, 20.0], "presentation_rect": [20.0, 80.0, 60.0, 20.0], "presentation": 1, "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "id": "obj-8"}}, {"box": {"maxclass": "message", "text": "call send_midi 144 4 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 230.0, 130.0, 22.0], "numinlets": 2, "id": "obj-9"}}, {"box": {"maxclass": "comment", "text": "2. <PERSON> OFF Pad 4", "presentation_rect": [10.0, 80.0, 120.0, 20.0], "presentation": 1, "patching_rect": [10.0, 200.0, 120.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-10"}}, {"box": {"maxclass": "button", "patching_rect": [200.0, 200.0, 20.0, 20.0], "presentation_rect": [20.0, 110.0, 80.0, 20.0], "presentation": 1, "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "id": "obj-11"}}, {"box": {"maxclass": "message", "text": "call release_control", "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 230.0, 120.0, 22.0], "numinlets": 2, "id": "obj-12"}}, {"box": {"maxclass": "comment", "text": "3. Release Control", "presentation_rect": [10.0, 110.0, 120.0, 20.0], "presentation": 1, "patching_rect": [180.0, 200.0, 120.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-13"}}, {"box": {"maxclass": "button", "patching_rect": [30.0, 280.0, 20.0, 20.0], "presentation_rect": [20.0, 150.0, 100.0, 20.0], "presentation": 1, "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "id": "obj-14"}}, {"box": {"maxclass": "message", "text": "call send_midi_sysex 240 71 127 21 96 0 1 247", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 310.0, 250.0, 22.0], "numinlets": 2, "id": "obj-15"}}, {"box": {"maxclass": "comment", "text": "4. <PERSON><PERSON>", "presentation_rect": [10.0, 150.0, 130.0, 20.0], "presentation": 1, "patching_rect": [10.0, 280.0, 130.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-16"}}, {"box": {"maxclass": "comment", "text": "Try these steps in order to reset the Push 2:", "presentation_rect": [10.0, 190.0, 300.0, 20.0], "presentation": 1, "patching_rect": [30.0, 350.0, 300.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 11.0, "id": "obj-17"}}, {"box": {"maxclass": "comment", "text": "If this doesn't work, physically disconnect and reconnect USB cable", "presentation_rect": [10.0, 210.0, 380.0, 20.0], "presentation": 1, "patching_rect": [30.0, 370.0, 380.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "id": "obj-18"}}], "lines": [{"patchline": {"source": ["obj-3", 0], "destination": ["obj-2", 0]}}, {"patchline": {"source": ["obj-2", 0], "destination": ["obj-4", 0]}}, {"patchline": {"source": ["obj-6", 0], "destination": ["obj-5", 0]}}, {"patchline": {"source": ["obj-5", 0], "destination": ["obj-4", 0]}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-9", 0]}}, {"patchline": {"source": ["obj-9", 0], "destination": ["obj-4", 0]}}, {"patchline": {"source": ["obj-11", 0], "destination": ["obj-12", 0]}}, {"patchline": {"source": ["obj-12", 0], "destination": ["obj-4", 0]}}, {"patchline": {"source": ["obj-14", 0], "destination": ["obj-15", 0]}}, {"patchline": {"source": ["obj-15", 0], "destination": ["obj-4", 0]}}], "dependency_cache": [], "autosave": 0}}