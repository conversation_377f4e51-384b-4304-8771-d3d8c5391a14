{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 6, "revision": 4, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [100.0, 100.0, 1000.0, 700.0], "bglocked": 0, "openinpresentation": 1, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"maxclass": "comment", "text": "Push 2 Clip <PERSON>", "presentation_rect": [10.0, 10.0, 250.0, 25.0], "presentation": 1, "patching_rect": [30.0, 30.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 18.0, "fontface": 1, "id": "obj-1"}}, {"box": {"maxclass": "comment", "text": "Hold Session button and press pads to queue clips", "presentation_rect": [10.0, 40.0, 300.0, 20.0], "presentation": 1, "patching_rect": [30.0, 50.0, 300.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 11.0, "id": "obj-2"}}, {"box": {"maxclass": "comment", "text": "Status:", "presentation_rect": [10.0, 70.0, 50.0, 20.0], "presentation": 1, "patching_rect": [30.0, 70.0, 50.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-3"}}, {"box": {"maxclass": "textedit", "text": "Initializing...", "presentation_rect": [65.0, 70.0, 200.0, 20.0], "presentation": 1, "patching_rect": [85.0, 70.0, 200.0, 20.0], "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", ""], "readonly": 1, "id": "obj-4"}}, {"box": {"maxclass": "comment", "text": "Session Button:", "presentation_rect": [10.0, 100.0, 100.0, 20.0], "presentation": 1, "patching_rect": [30.0, 100.0, 100.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-5"}}, {"box": {"maxclass": "led", "presentation_rect": [115.0, 100.0, 20.0, 20.0], "presentation": 1, "patching_rect": [135.0, 100.0, 20.0, 20.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "id": "obj-6"}}, {"box": {"maxclass": "comment", "text": "Queue:", "presentation_rect": [10.0, 130.0, 50.0, 20.0], "presentation": 1, "patching_rect": [30.0, 130.0, 50.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-7"}}, {"box": {"maxclass": "textedit", "text": "Empty", "presentation_rect": [65.0, 130.0, 200.0, 60.0], "presentation": 1, "patching_rect": [85.0, 130.0, 200.0, 60.0], "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", ""], "readonly": 1, "id": "obj-8"}}, {"box": {"maxclass": "comment", "text": "1. <PERSON><PERSON> <PERSON><PERSON>", "patching_rect": [30.0, 220.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-9"}}, {"box": {"maxclass": "message", "text": "path live_set devices 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 250.0, 120.0, 22.0], "numinlets": 2, "id": "obj-10"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 280.0, 62.0, 22.0], "numinlets": 2, "id": "obj-11", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route path", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 310.0, 62.0, 22.0], "numinlets": 1, "id": "obj-12"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend path", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 340.0, 78.0, 22.0], "numinlets": 1, "id": "obj-13"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s push_path", "numoutlets": 0, "patching_rect": [30.0, 370.0, 70.0, 22.0], "numinlets": 1, "id": "obj-14"}}, {"box": {"maxclass": "comment", "text": "2. <PERSON> <PERSON>", "patching_rect": [200.0, 220.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-15"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r push_path", "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 250.0, 68.0, 22.0], "numinlets": 0, "id": "obj-16"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Session", "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 280.0, 165.0, 22.0], "numinlets": 2, "id": "obj-17"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 310.0, 62.0, 22.0], "numinlets": 2, "id": "obj-18", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [200.0, 340.0, 155.0, 22.0], "numinlets": 1, "id": "obj-19"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [200.0, 370.0, 32.5, 22.0], "numinlets": 1, "id": "obj-20"}}, {"box": {"maxclass": "message", "text": "property value", "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 400.0, 79.0, 22.0], "numinlets": 2, "id": "obj-21"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.observer", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [200.0, 430.0, 72.0, 22.0], "numinlets": 2, "id": "obj-22", "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 127 0", "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [200.0, 460.0, 53.0, 22.0], "numinlets": 1, "id": "obj-23"}}, {"box": {"maxclass": "message", "text": "1", "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 490.0, 32.0, 22.0], "numinlets": 2, "id": "obj-24"}}, {"box": {"maxclass": "message", "text": "0", "numoutlets": 1, "outlettype": [""], "patching_rect": [234.0, 490.0, 32.0, 22.0], "numinlets": 2, "id": "obj-25"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s session_button", "numoutlets": 0, "patching_rect": [200.0, 520.0, 93.0, 22.0], "numinlets": 1, "id": "obj-26"}}, {"box": {"maxclass": "comment", "text": "3. <PERSON> Button Matrix", "patching_rect": [400.0, 220.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-27"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r push_path", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 250.0, 68.0, 22.0], "numinlets": 0, "id": "obj-28"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 280.0, 200.0, 22.0], "numinlets": 2, "id": "obj-29"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 310.0, 62.0, 22.0], "numinlets": 2, "id": "obj-30", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [400.0, 340.0, 155.0, 22.0], "numinlets": 1, "id": "obj-31"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print matrix_control_id", "numoutlets": 0, "patching_rect": [500.0, 370.0, 120.0, 22.0], "numinlets": 1, "id": "obj-32"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s matrix_control_id", "numoutlets": 0, "patching_rect": [400.0, 370.0, 108.0, 22.0], "numinlets": 1, "id": "obj-33"}}, {"box": {"maxclass": "comment", "text": "4. Control Grab/Release Logic", "patching_rect": [400.0, 410.0, 180.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-34"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r session_button", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 440.0, 91.0, 22.0], "numinlets": 0, "id": "obj-35"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r matrix_control_id", "numoutlets": 1, "outlettype": [""], "patching_rect": [500.0, 440.0, 106.0, 22.0], "numinlets": 0, "id": "obj-36"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r push_path", "numoutlets": 1, "outlettype": [""], "patching_rect": [620.0, 440.0, 68.0, 22.0], "numinlets": 0, "id": "obj-37"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print grab_debug", "numoutlets": 0, "patching_rect": [450.0, 470.0, 100.0, 22.0], "numinlets": 1, "id": "obj-38"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 1 0", "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [400.0, 500.0, 53.0, 22.0], "numinlets": 1, "id": "obj-39"}}, {"box": {"maxclass": "message", "text": "call grab_control $1", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 530.0, 110.0, 22.0], "numinlets": 2, "id": "obj-40"}}, {"box": {"maxclass": "message", "text": "call release_control $1", "numoutlets": 1, "outlettype": [""], "patching_rect": [434.0, 530.0, 125.0, 22.0], "numinlets": 2, "id": "obj-41"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 560.0, 62.0, 22.0], "numinlets": 2, "id": "obj-42", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print grab_status", "numoutlets": 0, "patching_rect": [400.0, 590.0, 95.0, 22.0], "numinlets": 1, "id": "obj-43"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s grab_status", "numoutlets": 0, "patching_rect": [500.0, 590.0, 78.0, 22.0], "numinlets": 1, "id": "obj-44"}}, {"box": {"maxclass": "comment", "text": "5. Matrix Value Observer & Interceptor", "patching_rect": [650.0, 220.0, 220.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-45"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r matrix_control_id", "numoutlets": 1, "outlettype": [""], "patching_rect": [650.0, 250.0, 106.0, 22.0], "numinlets": 0, "id": "obj-46"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [650.0, 280.0, 32.5, 22.0], "numinlets": 1, "id": "obj-47"}}, {"box": {"maxclass": "message", "text": "property value", "numoutlets": 1, "outlettype": [""], "patching_rect": [650.0, 310.0, 79.0, 22.0], "numinlets": 2, "id": "obj-48"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.observer", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [650.0, 340.0, 72.0, 22.0], "numinlets": 2, "id": "obj-49", "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print matrix_values", "numoutlets": 0, "patching_rect": [650.0, 370.0, 100.0, 22.0], "numinlets": 1, "id": "obj-50"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r grab_status", "numoutlets": 1, "outlettype": [""], "patching_rect": [750.0, 370.0, 76.0, 22.0], "numinlets": 0, "id": "obj-51"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "gate 1 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [650.0, 400.0, 50.0, 22.0], "numinlets": 2, "id": "obj-52"}}, {"box": {"maxclass": "comment", "text": "Gate blocks matrix values when grabbed (status=1)", "patching_rect": [710.0, 400.0, 280.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-53"}}, {"box": {"maxclass": "comment", "text": "6. <PERSON><PERSON> Logic", "patching_rect": [650.0, 440.0, 100.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-54"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "coll queue_list", "numoutlets": 4, "outlettype": ["", "", "", ""], "patching_rect": [650.0, 470.0, 85.0, 22.0], "numinlets": 1, "id": "obj-55", "saved_object_attributes": {"embed": 1, "precision": 6}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sprintf Queue: %ld clips", "numoutlets": 1, "outlettype": [""], "patching_rect": [650.0, 500.0, 130.0, 22.0], "numinlets": 1, "id": "obj-56"}}, {"box": {"maxclass": "message", "text": "set text $1", "numoutlets": 1, "outlettype": [""], "patching_rect": [650.0, 530.0, 70.0, 22.0], "numinlets": 2, "id": "obj-57"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s queue_display", "numoutlets": 0, "patching_rect": [650.0, 560.0, 90.0, 22.0], "numinlets": 1, "id": "obj-58"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r status_update", "numoutlets": 1, "outlettype": [""], "patching_rect": [85.0, 50.0, 86.0, 22.0], "numinlets": 0, "id": "obj-59"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r session_led", "numoutlets": 1, "outlettype": [""], "patching_rect": [135.0, 80.0, 76.0, 22.0], "numinlets": 0, "id": "obj-60"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r queue_display", "numoutlets": 1, "outlettype": [""], "patching_rect": [85.0, 110.0, 88.0, 22.0], "numinlets": 0, "id": "obj-61"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s status_update", "numoutlets": 0, "patching_rect": [250.0, 180.0, 88.0, 22.0], "numinlets": 1, "id": "obj-62"}}, {"box": {"maxclass": "message", "text": "set text Push 2 Connected", "numoutlets": 1, "outlettype": [""], "patching_rect": [250.0, 150.0, 125.0, 22.0], "numinlets": 2, "id": "obj-63"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s session_led", "numoutlets": 0, "patching_rect": [300.0, 520.0, 78.0, 22.0], "numinlets": 1, "id": "obj-64"}}, {"box": {"maxclass": "loadbang", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [30.0, 200.0, 60.0, 22.0], "numinlets": 1, "id": "obj-65"}}], "lines": [{"patchline": {"destination": ["obj-10", 0], "source": ["obj-65", 0]}}, {"patchline": {"destination": ["obj-11", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-12", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-63", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-22", 1], "source": ["obj-20", 1]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-23", 1]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-64", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-64", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-40", 1], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-41", 1], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-42", 1], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-39", 1]}}, {"patchline": {"destination": ["obj-42", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-42", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-43", 0], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-47", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-47", 0]}}, {"patchline": {"destination": ["obj-49", 1], "source": ["obj-47", 1]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-52", 1], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-56", 0], "source": ["obj-55", 1]}}, {"patchline": {"destination": ["obj-57", 0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-61", 0]}}, {"patchline": {"destination": ["obj-62", 0], "source": ["obj-63", 0]}}]}}