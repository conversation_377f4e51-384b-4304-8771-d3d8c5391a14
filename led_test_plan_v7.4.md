# LED Test Plan v7.4 - NOTEOUT MIDI Direct Control

## Status: TESTING - Hardware Implementation

## Overview
**LED Control v7.4** uses direct MIDI output via `noteout` object to control Push 2 LEDs. This bypasses all Live API issues and sends MIDI note-on messages directly to the Push 2 hardware.

## Technical Implementation

### Max Patch Components:
1. **`route led_debug call`** (obj-209a) - Routes LED commands from JavaScript
2. **`unpack i i i`** (obj-209h) - Separates scene, track, velocity from JavaScript
3. **`expr 36 + $i1 * 8 + $i2`** (obj-209i) - Converts scene/track to MIDI note number
4. **`makenote 127 10`** (obj-209g) - Creates note-on/note-off MIDI messages  
5. **`noteout 1 "Ableton Push 2"`** (obj-209f) - Sends MIDI directly to Push 2 on channel 1

### JavaScript Functions:
- **`send_test_led_commands()`** - Sends `outlet(0, "call", scene, track, velocity)`
- **`enter_session_mode()`** - Activates LED preservation system
- **`exit_session_mode()`** - Clears LEDs and restores normal behavior

### Data Flow:
```
JavaScript: outlet(0, "call", scene, track, velocity)
    ↓
Max route: separates "call" messages
    ↓  
unpack: scene(0-7), track(0-7), velocity(0-127)
    ↓
expr: converts to MIDI note (36-99 for 8x8 pad matrix)
    ↓
makenote: creates note-on message with velocity
    ↓
noteout: sends to "Ableton Push 2" MIDI port on channel 1
```

## Expected Behavior

### When Session Button Pressed:
1. **JavaScript logs:** "Entering Session mode", "Found clip at scene X track Y", "LED control: scene X track Y color Z"
2. **Max console shows:** "MIDI_OUT: X Y Z" messages 
3. **Push 2 LEDs:** Pads in test area (top-left 2x2) should light up based on clip presence
   - **Red (127):** Playing clips
   - **Green (126):** Stopped clips
   - **Off (0):** Empty slots

### When Session Button Released:
1. **JavaScript logs:** "Exiting Session mode", "Cleared test area LEDs"
2. **Max console shows:** "MIDI_OUT: X Y 0" (turning off LEDs)
3. **Push 2 LEDs:** Test area LEDs should turn off

## Troubleshooting

### If No LEDs Light Up:
1. **Check MIDI port name:** Max console should show no "noteout: no such port" errors
2. **Verify Push 2 connection:** Check Push 2 is connected and recognized by system
3. **Test MIDI output:** Look for "MIDI_OUT:" messages in Max console
4. **Check note range:** Push 2 pads use MIDI notes 36-99 (our formula: 36 + scene*8 + track)

### Common Issues:
- **Port name mismatch:** "Ableton Push 2" might need adjustment based on system
- **MIDI channel:** Using channel 1 (standard for Push 2 pads)
- **Note timing:** `makenote 127 10` sends 10ms note duration

## Success Criteria
- [ ] No "live.object doesn't understand" errors
- [ ] MIDI_OUT messages appear in Max console
- [ ] Push 2 LEDs light up in test area (top-left 2x2 pads)
- [ ] LEDs turn off when Session mode exits
- [ ] Different colors for playing vs stopped clips

## Next Steps if Successful
1. **Expand to full 8x8 matrix** - Change test loop from 2x2 to full pad matrix
2. **Color palette optimization** - Test different velocity values for better color distinction
3. **Performance tuning** - Adjust continuous update timing if needed
4. **Integration testing** - Verify LED system doesn't interfere with Session + Play functionality

## Technical Notes
- **MIDI Format:** Note-on messages on channel 1, notes 36-99, velocity 0-127
- **Push 2 Color Map:** Velocity values map to different LED colors
- **No Live API dependency:** Direct hardware communication bypasses all Live API issues
- **Backwards compatible:** Core Session + Play functionality remains unchanged
