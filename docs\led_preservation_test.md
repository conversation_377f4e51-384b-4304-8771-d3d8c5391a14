# Push2ClipQueue Test Plan - Pad LED Preservation

## Test: Basic LED Preservation During Session Mode

### Setup:
1. Open clipQueue.maxpat in Max for Live
2. Ensure Push 2 is connected
3. Have at least a few clips available in Live set

### Test Steps:

#### Test 1: Enter Session Mode
1. **Action:** Press and hold Session button
2. **Expected Result:** 
   - Pad LEDs should show current clip states
   - Green LEDs for stopped clips
   - Red LEDs for playing clips  
   - Off LEDs for empty clip slots
   - Debug console should show "DEBUG: Entering Session mode - preserving pad lights"

#### Test 2: LED Updates During Session Mode
1. **Setup:** Keep Session button held
2. **Action:** Start/stop clips in Live using mouse or other controller
3. **Expected Result:**
   - Pad LEDs should update in real-time
   - Color changes should reflect clip state changes
   - Debug console should show "DEBUG: Clip state changed during Session mode"

#### Test 3: Exit Session Mode
1. **Action:** Release Session button
2. **Expected Result:**
   - All manual LED control should be cleared
   - Normal Push 2 LED behavior should resume
   - Debug console should show "DEBUG: Exiting Session mode - restoring normal LED behavior"

#### Test 4: Session + Play with LED Preservation
1. **Action:** Hold Session button, observe LEDs, then press Play
2. **Expected Result:**
   - LEDs show available clips while Session is held
   - Session + Play triggers launch as expected
   - LEDs return to normal after release

### Debug Console Messages to Look For:
- "DEBUG: Initializing clip observers for pad light preservation"
- "DEBUG: Getting current clip states for pad lighting"
- "DEBUG: Updating pad LEDs for Session mode"
- "DEBUG: Entering Session mode - preserving pad lights"
- "DEBUG: Exiting Session mode - restoring normal LED behavior"

### Success Criteria:
✅ LEDs show clip availability during Session mode
✅ LEDs update in real-time with clip changes
✅ Normal LED behavior resumes after Session release
✅ No interference with existing Session + Play functionality
