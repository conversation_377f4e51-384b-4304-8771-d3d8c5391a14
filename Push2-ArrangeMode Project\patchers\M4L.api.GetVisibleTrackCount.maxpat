{"patcher": {"fileversion": 1, "rect": [688.0, 81.0, 490.0, 385.0], "bglocked": 0, "defrect": [688.0, 81.0, 490.0, 385.0], "openrect": [0.0, 0.0, 0.0, 0.0], "openinpresentation": 0, "default_fontsize": 10.0, "default_fontface": 0, "default_fontname": "Arial Bold", "gridonopen": 0, "gridsize": [8.0, 8.0], "gridsnaponopen": 0, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "boxes": [{"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route bang <empty>", "outlettype": ["", "", ""], "patching_rect": [144.0, 272.0, 106.0, 18.0], "id": "obj-28", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 3, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "-->", "patching_rect": [120.0, 296.0, 23.0, 18.0], "id": "obj-15", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "the list has the format \"id 1 id 2 id 3\" - so we get rid of the \"id\" tokens and count the members", "linecount": 5, "patching_rect": [16.0, 296.0, 107.0, 64.0], "id": "obj-14", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "< after the live.object points to the current Live Set we ask it for the list of visible tracks", "linecount": 2, "patching_rect": [264.0, 216.0, 208.0, 29.0], "id": "obj-10", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "help live.object", "outlettype": [""], "patching_rect": [264.0, 248.0, 83.0, 16.0], "id": "obj-12", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "bgcolor": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pcontrol", "outlettype": [""], "patching_rect": [264.0, 272.0, 50.0, 18.0], "hidden": 1, "id": "obj-13", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "< first we set the live.path to the current Live Set and feed the live.object below with its ID", "linecount": 2, "patching_rect": [264.0, 136.0, 214.0, 29.0], "id": "obj-53", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "help live.path", "outlettype": [""], "patching_rect": [264.0, 168.0, 74.0, 16.0], "id": "obj-52", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "bgcolor": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pcontrol", "outlettype": [""], "patching_rect": [264.0, 192.0, 50.0, 18.0], "hidden": 1, "id": "obj-51", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "prototypename": "ML.subpatcher-title", "text": "Get Visible Track Count", "patching_rect": [8.0, 16.0, 292.0, 34.0], "id": "obj-48", "frgb": [0.3, 0.34, 0.4, 1.0], "fontname": "Arial Bold Italic", "numinlets": 1, "textcolor": [0.3, 0.34, 0.4, 1.0], "numoutlets": 0, "fontsize": 24.0}}, {"box": {"maxclass": "comment", "prototypename": "<PERSON><PERSON><PERSON>patcher-story", "text": "Get the number of the visible tracks in the current Live set.", "patching_rect": [8.0, 48.0, 294.0, 19.0], "id": "obj-50", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "Arial Italic", "numinlets": 1, "numoutlets": 0, "fontsize": 11.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "outlettype": ["bang", ""], "patching_rect": [184.0, 160.0, 33.0, 18.0], "id": "obj-11", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "outlet", "patching_rect": [144.0, 352.0, 18.0, 18.0], "id": "obj-9", "numinlets": 1, "numoutlets": 0, "comment": ""}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl len", "outlettype": ["", ""], "patching_rect": [144.0, 320.0, 37.0, 18.0], "id": "obj-8", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl delace", "outlettype": ["", ""], "patching_rect": [144.0, 296.0, 53.0, 18.0], "id": "obj-7", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route visible_tracks", "outlettype": ["", ""], "patching_rect": [144.0, 248.0, 104.0, 18.0], "id": "obj-6", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "get visible_tracks", "outlettype": [""], "patching_rect": [104.0, 192.0, 95.0, 16.0], "id": "obj-5", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "path live_set", "outlettype": [""], "patching_rect": [160.0, 112.0, 71.0, 16.0], "id": "obj-4", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "outlettype": ["", "", ""], "color": [0.984314, 0.819608, 0.05098, 1.0], "patching_rect": [160.0, 136.0, 67.0, 18.0], "id": "obj-3", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 3, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "outlettype": [""], "color": [0.984314, 0.819608, 0.05098, 1.0], "patching_rect": [144.0, 216.0, 73.0, 18.0], "id": "obj-2", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "inlet", "outlettype": [""], "patching_rect": [160.0, 80.0, 18.0, 18.0], "id": "obj-1", "numinlets": 0, "numoutlets": 1, "comment": ""}}], "lines": [{"patchline": {"source": ["obj-28", 2], "destination": ["obj-7", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-6", 0], "destination": ["obj-28", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-2", 0], "destination": ["obj-6", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-3", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-5", 0], "destination": ["obj-2", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-3", 1], "destination": ["obj-11", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-1", 0], "destination": ["obj-4", 0], "hidden": 0, "midpoints": [169.5, 112.0, 169.5, 112.0]}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-9", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-11", 0], "destination": ["obj-5", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-11", 1], "destination": ["obj-2", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-7", 1], "destination": ["obj-8", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-52", 0], "destination": ["obj-51", 0], "hidden": 1, "midpoints": []}}, {"patchline": {"source": ["obj-12", 0], "destination": ["obj-13", 0], "hidden": 1, "midpoints": []}}]}}