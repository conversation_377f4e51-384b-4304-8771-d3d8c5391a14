# Transport Restart Issue Research and Solutions

## Problem Statement

When implementing Session + Play functionality, we discovered that certain Ableton Live API calls cause unwanted transport restarts during playback. This document tracks our research and solutions.

## Root Cause Analysis - UPDATED JUNE 26, 2025

### The Transport Restart Problem

**Transport restarts occur when:**
1. Modifying clip `launch_quantization` during transport playback **← CONFIRMED PRIMARY CAUSE**
2. Using `set_fire_button_state` on clips with immediate quantization (0) during playback
3. Changing global `clip_trigger_quantization` while transport is running

**CRITICAL DISCOVERY:** The `launch_single_queued_clip()` function in `clipLauncher.js` around line 770 contains:
```javascript
// THIS CAUSES TRANSPORT RESTART DURING PLAYBACK:
clipObject.set("launch_quantization", 0);  // ← RESTART TRIGGER
```

This explains why Push Play auto-launch causes transport restarts during playback.

## Research Findings - UPDATED

### Safe vs Unsafe API Operations During Playback

#### ✅ **Safe Operations (No Transport Restart):**
- `ClipSlot.fire()` without quantization modification **← PROVEN WORKING**
- Reading clip properties (`get("launch_quantization")`)
- `launch_all_queued()` function (uses ClipSlot.fire() without quantization modification)
- Scene launching (but fires unwanted clips)

#### ❌ **Unsafe Operations (Cause Transport Restart):**
- `clip.set("launch_quantization", 0)` during playback **← CONFIRMED IN CODE**
- `live_set.set("clip_trigger_quantization", 0)` during playback 
- `launch_single_queued_clip()` function during playback **← ROOT CAUSE**

## Working vs Broken Code Paths - NEW SECTION

### ✅ **WORKING: "Launch All Queued" Button**
```javascript
function launch_all_queued() {
    // Uses prepare_clip_for_launch() with NO quantization modification
    // Fires clips using ClipSlot.fire() in deferlow context
    // Result: Perfect sync, no transport restart
}
```

### ❌ **BROKEN: Push Play Auto-Launch**
```javascript
function launch_single_queued_clip(track, scene) {
    // Uses clipObject.set("launch_quantization", 0) ← RESTART!
    // Result: Transport restarts to 1.1.1 during playback
}
```

## IMMEDIATE SOLUTIONS - NEW SECTION

### 1. **HIGH PRIORITY: Fix Push Play Auto-Launch**
**Problem:** Uses `launch_single_queued_clip()` which modifies quantization
**Solution:** Make Push Play use same code path as working "Launch All Queued" button

**Implementation:**
- Change Push Play routing to call `launch_all_queued` instead of individual clip launching
- Remove or bypass `launch_single_queued_clip()` for auto-launch triggers
- Test transport-safe launching during playback

### 2. **HIGH PRIORITY: Debug Session + Play Execution Context**
**Problem:** Same `launch_all_queued()` function works from button but not Session + Play
**Investigation:** Add comprehensive debug logging to compare execution contexts

### 3. **MEDIUM PRIORITY: Fix Transport Position Dependency**
**Problem:** Session + Play immediate launch only works from 1.1.1
**Options:**
- Reset transport to 1.1.1 before launching
- Use ClipSlot.fire() instead of Clip.fire()
- Research set_fire_button_state for immediate launch

## Current Status - JUNE 26, 2025

### ✅ **Working Features:**
- Basic clip queueing
- "Launch All Queued" button (even during playback)
- Session + Play (when transport stopped at 1.1.1)
- UI and debug features

### ❌ **Broken Features:**
- Push Play auto-launch (quantization override causes restart)
- Session + Play during playback (execution context issue)
- Session + Play transport position dependency (only works from 1.1.1)

### 🎯 **Next Steps:**
1. Fix Push Play to use `launch_all_queued()` code path
2. Debug Session + Play execution context differences
3. Research transport position independent immediate launch

**Priority:** Critical - Core Session + Play feature has multiple blocking issues

### 4. Use Identical Logic to Auto-Launch
**Approach:** Session + Play during playback uses exact same method as auto-launch
**Result:** Still restarts because auto-launch itself has the quantization modification issue

## The Core Problem

**The fundamental issue:** Our auto-launch function modifies clip quantization during playback, which causes transport restarts. We need to find a method that:

1. **Launches multiple clips simultaneously** (for sync)
2. **Respects existing quantization** (for proper timing)
3. **Works during transport playback** (no restarts)
4. **Provides immediate launch capability** (for Session + Play when stopped)

## Current Research Direction

### Option 1: Use ClipSlot.fire() Without Quantization Override
```javascript
// Instead of modifying quantization:
// clip.set("launch_quantization", 0);  // CAUSES RESTART
// clip.liveObject.call("fire");

// Just fire with existing quantization:
clipSlot.call("fire");  // Safe during playback
```

**Pros:** No transport restart  
**Cons:** Respects existing clip quantization, may not be immediate

### Option 2: Use set_fire_button_state Without Quantization Changes
```javascript
// On Clip objects (not ClipSlot) without modifying quantization
clipObject.call("set_fire_button_state", 1);
```

**Research needed:** Does this respect quantization properly without modification?

### Option 3: Simulate Hardware Button Presses
**Research needed:** How to simulate actual Push button matrix presses through Max

## Next Steps

1. **Test ClipSlot.fire() without any quantization override** for Session + Play during playback
2. **Research hardware button simulation** through Max objects
3. **Test set_fire_button_state on Clip objects** without quantization modification
4. **Document which methods work safely during transport playback**

## Documentation Updates Needed

- Update `ableton_internal_queuing_research.md` with transport restart findings
- Add safe API methods list
- Document the quantization modification restriction
- Create testing guide for transport-safe operations

## Key Insight

**The solution may be accepting that quantization cannot be overridden during playback.** Instead, we should:

- **Session + Play when stopped:** Use immediate launch with quantization override
- **Session + Play when playing:** Use existing quantization without modification
- **Auto-launch:** Remove quantization override to prevent transport restarts

This would provide a clean, transport-safe implementation that works with Live's quantization system rather than fighting it.

---

**Date:** June 25, 2025  
**Status:** Active Research  
**Priority:** Critical (blocking Session + Play feature completion)
