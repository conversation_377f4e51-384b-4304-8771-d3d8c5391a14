{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 87.0, 800.0, 600.0], "bglocked": 0, "openinpresentation": 1, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"maxclass": "comment", "text": "Push 2 Clip <PERSON>", "presentation_rect": [10.0, 10.0, 250.0, 25.0], "presentation": 1, "patching_rect": [30.0, 30.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 18.0, "fontface": 1, "id": "obj-1"}}, {"box": {"maxclass": "comment", "text": "Hold Session button and press pads to queue clips", "presentation_rect": [10.0, 40.0, 300.0, 20.0], "presentation": 1, "patching_rect": [30.0, 50.0, 300.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 11.0, "id": "obj-46"}}, {"box": {"maxclass": "comment", "text": "Status:", "presentation_rect": [10.0, 70.0, 50.0, 20.0], "presentation": 1, "patching_rect": [30.0, 70.0, 50.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-47"}}, {"box": {"maxclass": "textedit", "text": "Initializing...", "presentation_rect": [65.0, 70.0, 200.0, 20.0], "presentation": 1, "patching_rect": [85.0, 70.0, 200.0, 20.0], "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", ""], "readonly": 1, "id": "obj-48"}}, {"box": {"maxclass": "comment", "text": "Session Button:", "presentation_rect": [10.0, 100.0, 100.0, 20.0], "presentation": 1, "patching_rect": [30.0, 100.0, 100.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-49"}}, {"box": {"maxclass": "led", "presentation_rect": [115.0, 102.0, 16.0, 16.0], "presentation": 1, "patching_rect": [135.0, 102.0, 16.0, 16.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "id": "obj-50"}}, {"box": {"maxclass": "comment", "text": "Queued Clips:", "presentation_rect": [10.0, 130.0, 100.0, 20.0], "presentation": 1, "patching_rect": [30.0, 130.0, 100.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-51"}}, {"box": {"maxclass": "textedit", "presentation_rect": [10.0, 155.0, 300.0, 150.0], "presentation": 1, "patching_rect": [30.0, 155.0, 300.0, 150.0], "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", ""], "text": "No clips queued", "readonly": 1, "id": "obj-52"}}, {"box": {"maxclass": "comment", "text": "1. Initialize Push2 connection", "patching_rect": [30.0, 90.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-2"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadbang", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [30.0, 120.0, 58.0, 22.0], "numinlets": 1, "id": "obj-3"}}, {"box": {"maxclass": "message", "text": "path control_surfaces 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 150.0, 127.0, 22.0], "numinlets": 2, "id": "obj-4"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "saved_object_attributes": {"_persistence": 1, "parameter_enable": 1}, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [30.0, 180.0, 67.0, 22.0], "id": "obj-5", "color": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [30.0, 210.0, 32.5, 22.0], "numinlets": 1, "id": "obj-6"}}, {"box": {"id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [70.0, 210.0, 76.0, 22.0], "text": "s ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [100.0, 270.0, 74.0, 22.0], "text": "r ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-45", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [470.0, 120.0, 74.0, 22.0], "text": "r ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"maxclass": "comment", "text": "2. Get Session button control", "patching_rect": [30.0, 240.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-7"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Session_Mode_Button", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 270.0, 245.0, 22.0], "numinlets": 2, "id": "obj-8"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 300.0, 62.0, 22.0], "numinlets": 2, "id": "obj-9", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 330.0, 155.0, 22.0], "numinlets": 1, "id": "obj-10"}}, {"box": {"id": "obj-37", "maxclass": "print", "numinlets": 1, "numoutlets": 0, "patching_rect": [30.0, 355.0, 125.0, 22.0], "text": "session_button_id"}}, {"box": {"maxclass": "comment", "text": "3. Observe button state", "patching_rect": [30.0, 360.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-11"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [30.0, 390.0, 32.5, 22.0], "numinlets": 1, "id": "obj-12"}}, {"box": {"maxclass": "message", "text": "property value", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 420.0, 79.0, 22.0], "numinlets": 2, "id": "obj-13"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.observer", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 450.0, 72.0, 22.0], "numinlets": 2, "id": "obj-14", "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "comment", "text": "4. <PERSON>le button state", "patching_rect": [30.0, 480.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-15"}}, {"box": {"maxclass": "comment", "text": "Convert button values: 127->1 (pressed), 0->0 (released)", "patching_rect": [90.0, 510.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-20"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 127 0", "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [30.0, 510.0, 53.0, 22.0], "numinlets": 1, "id": "obj-16"}}, {"box": {"maxclass": "message", "text": "1", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 540.0, 32.0, 22.0], "numinlets": 2, "id": "obj-17"}}, {"box": {"maxclass": "message", "text": "0", "numoutlets": 1, "outlettype": [""], "patching_rect": [64.0, 540.0, 32.0, 22.0], "numinlets": 2, "id": "obj-18"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s session_button", "numoutlets": 0, "patching_rect": [30.0, 570.0, 93.0, 22.0], "numinlets": 1, "id": "obj-19"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print session_button_send", "numoutlets": 0, "patching_rect": [30.0, 600.0, 140.0, 22.0], "numinlets": 1, "id": "obj-30"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s session_led", "numoutlets": 0, "patching_rect": [120.0, 570.0, 78.0, 22.0], "numinlets": 1, "id": "obj-53"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r session_led", "numoutlets": 1, "outlettype": [""], "patching_rect": [135.0, 80.0, 76.0, 22.0], "numinlets": 0, "id": "obj-54"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s status_update", "numoutlets": 0, "patching_rect": [250.0, 180.0, 88.0, 22.0], "numinlets": 1, "id": "obj-55"}}, {"box": {"maxclass": "message", "text": "set text Push 2 Connected", "numoutlets": 1, "outlettype": [""], "patching_rect": [250.0, 150.0, 125.0, 22.0], "numinlets": 2, "id": "obj-57"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r status_update", "numoutlets": 1, "outlettype": [""], "patching_rect": [85.0, 50.0, 86.0, 22.0], "numinlets": 0, "id": "obj-56"}}, {"box": {"maxclass": "comment", "text": "5. <PERSON> Button Matrix", "patching_rect": [400.0, 90.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-21"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 120.0, 200.0, 22.0], "numinlets": 2, "id": "obj-22"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 150.0, 62.0, 22.0], "numinlets": 2, "id": "obj-23", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [400.0, 180.0, 155.0, 22.0], "numinlets": 1, "id": "obj-24"}}, {"box": {"id": "obj-38", "maxclass": "print", "numinlets": 1, "numoutlets": 0, "patching_rect": [500.0, 180.0, 108.0, 22.0], "text": "matrix_button_id"}}, {"box": {"id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 330.0, 91.0, 22.0], "text": "r session_button"}}, {"box": {"id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [520.0, 360.0, 150.0, 22.0], "text": "Push-Release_Grab_A_Control"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print control_grab_status", "numoutlets": 0, "patching_rect": [520.0, 390.0, 135.0, 22.0], "numinlets": 1, "id": "obj-61"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 1 0", "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [680.0, 330.0, 53.0, 22.0], "numinlets": 1, "id": "obj-62"}}, {"box": {"maxclass": "message", "text": "set text Controls Grabbed - Queue Mode Active", "numoutlets": 1, "outlettype": [""], "patching_rect": [680.0, 360.0, 250.0, 22.0], "numinlets": 2, "id": "obj-63"}}, {"box": {"maxclass": "message", "text": "set text Controls Released - Normal Mode", "numoutlets": 1, "outlettype": [""], "patching_rect": [720.0, 390.0, 210.0, 22.0], "numinlets": 2, "id": "obj-64"}}, {"box": {"id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [180.0, 240.0, 69.0, 22.0], "text": "delay 100"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [400.0, 210.0, 32.5, 22.0], "numinlets": 1, "id": "obj-25"}}, {"box": {"maxclass": "message", "text": "property value", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 285.0, 79.0, 22.0], "numinlets": 2, "id": "obj-26"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.observer", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [400.0, 315.0, 72.0, 22.0], "numinlets": 2, "id": "obj-27", "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "comment", "text": "6. Process matrix values", "patching_rect": [400.0, 345.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-28"}}, {"box": {"maxclass": "comment", "text": "7. Route matrix values based on Session button", "patching_rect": [400.0, 405.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-31"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r session_button", "numoutlets": 1, "outlettype": [""], "patching_rect": [500.0, 435.0, 91.0, 22.0], "numinlets": 0, "id": "obj-32"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "+ 1", "numoutlets": 1, "outlettype": ["int"], "patching_rect": [500.0, 460.0, 29.0, 22.0], "numinlets": 2, "id": "obj-32a"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "gate 2 1", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [400.0, 465.0, 50.0, 22.0], "numinlets": 2, "id": "obj-33"}}, {"box": {"maxclass": "comment", "text": "Gate 1: Normal mode (forward to Live)", "patching_rect": [320.0, 495.0, 200.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-34a"}}, {"box": {"maxclass": "comment", "text": "Gate 2: Queue mode (store for later)", "patching_rect": [460.0, 495.0, 200.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-34b"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print matrix_normal", "numoutlets": 0, "patching_rect": [320.0, 525.0, 105.0, 22.0], "numinlets": 1, "id": "obj-34"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "unpack i i i i", "numoutlets": 4, "outlettype": ["int", "int", "int", "int"], "patching_rect": [320.0, 555.0, 71.0, 22.0], "numinlets": 1, "id": "obj-29a"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print matrix_queue", "numoutlets": 0, "patching_rect": [460.0, 525.0, 100.0, 22.0], "numinlets": 1, "id": "obj-34c"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "unpack i i i i", "numoutlets": 4, "outlettype": ["int", "int", "int", "int"], "patching_rect": [460.0, 555.0, 71.0, 22.0], "numinlets": 1, "id": "obj-29b"}}, {"box": {"maxclass": "comment", "text": "Only queue button presses (velocity > 0), not releases", "patching_rect": [540.0, 580.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-90"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "if $i1 > 0 then bang", "numoutlets": 1, "outlettype": [""], "patching_rect": [460.0, 580.0, 105.0, 22.0], "numinlets": 1, "id": "obj-91"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "gate 1 1", "numoutlets": 1, "outlettype": [""], "patching_rect": [500.0, 610.0, 50.0, 22.0], "numinlets": 2, "id": "obj-92"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print gate_output", "numoutlets": 0, "patching_rect": [500.0, 635.0, 90.0, 22.0], "numinlets": 1, "id": "obj-106"}}, {"box": {"maxclass": "message", "text": "0", "numoutlets": 1, "outlettype": [""], "patching_rect": [460.0, 610.0, 32.0, 22.0], "numinlets": 2, "id": "obj-93"}}, {"box": {"maxclass": "message", "text": "1", "numoutlets": 1, "outlettype": [""], "patching_rect": [496.0, 610.0, 32.0, 22.0], "numinlets": 2, "id": "obj-94"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sprintf set text Clip at %d,%d queued", "numoutlets": 1, "outlettype": [""], "patching_rect": [500.0, 640.0, 180.0, 22.0], "numinlets": 2, "id": "obj-58"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t s b", "numoutlets": 2, "outlettype": ["", "bang"], "patching_rect": [460.0, 670.0, 35.0, 22.0], "numinlets": 1, "id": "obj-108"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r session_button", "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 175.0, 85.0, 22.0], "numinlets": 0, "id": "obj-113"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "change", "numoutlets": 3, "outlettype": ["", "int", "int"], "patching_rect": [200.0, 200.0, 50.0, 22.0], "numinlets": 1, "id": "obj-110"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 1", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [200.0, 225.0, 35.0, 22.0], "numinlets": 2, "id": "obj-111"}}, {"box": {"maxclass": "message", "text": "clear", "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 250.0, 34.0, 22.0], "numinlets": 2, "id": "obj-112"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print to_coll", "numoutlets": 0, "patching_rect": [460.0, 665.0, 70.0, 22.0], "numinlets": 1, "id": "obj-107"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "coll clip_queue", "numoutlets": 4, "outlettype": ["", "", "", ""], "patching_rect": [460.0, 690.0, 86.0, 22.0], "numinlets": 1, "id": "obj-95"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print coll_output", "numoutlets": 0, "patching_rect": [460.0, 715.0, 85.0, 22.0], "numinlets": 1, "id": "obj-105"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pack i i i", "numoutlets": 1, "outlettype": [""], "patching_rect": [460.0, 610.0, 58.0, 22.0], "numinlets": 3, "id": "obj-106"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "counter 0", "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [400.0, 610.0, 55.0, 22.0], "numinlets": 5, "id": "obj-131"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "+ 1", "numoutlets": 1, "outlettype": ["int"], "patching_rect": [400.0, 635.0, 25.0, 22.0], "numinlets": 2, "id": "obj-134"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "gate 1 1", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 580.0, 52.0, 22.0], "numinlets": 2, "id": "obj-133"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sprintf store %d %d %d %d", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 640.0, 120.0, 22.0], "numinlets": 4, "id": "obj-132"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sprintf store %d %d %d", "numoutlets": 1, "outlettype": [""], "patching_rect": [460.0, 640.0, 110.0, 22.0], "numinlets": 3, "id": "obj-96"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print sprintf_output", "numoutlets": 0, "patching_rect": [460.0, 665.0, 100.0, 22.0], "numinlets": 1, "id": "obj-104"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [460.0, 700.0, 25.0, 22.0], "numinlets": 1, "id": "obj-97"}}, {"box": {"maxclass": "message", "text": "clear", "numoutlets": 1, "outlettype": [""], "patching_rect": [460.0, 730.0, 34.0, 22.0], "numinlets": 2, "id": "obj-98"}}, {"box": {"maxclass": "message", "text": "set text", "numoutlets": 1, "outlettype": [""], "patching_rect": [540.0, 730.0, 48.0, 22.0], "numinlets": 2, "id": "obj-126"}}, {"box": {"maxclass": "message", "text": "dump", "numoutlets": 1, "outlettype": [""], "patching_rect": [496.0, 730.0, 36.0, 22.0], "numinlets": 2, "id": "obj-99"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "delay 1", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [496.0, 700.0, 45.0, 22.0], "numinlets": 2, "id": "obj-delay1"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "unpack i i i", "numoutlets": 3, "outlettype": ["int", "int", "int"], "patching_rect": [460.0, 760.0, 62.0, 22.0], "numinlets": 1, "id": "obj-102"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "regexp (.+)_(.+)", "numoutlets": 5, "outlettype": ["", "", "", "", ""], "patching_rect": [460.0, 815.0, 90.0, 22.0], "numinlets": 1, "id": "obj-109"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sprintf Clip at %d,%d", "numoutlets": 1, "outlettype": [""], "patching_rect": [490.0, 785.0, 105.0, 22.0], "numinlets": 2, "id": "obj-103"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t s b", "numoutlets": 2, "outlettype": ["", "bang"], "patching_rect": [460.0, 810.0, 35.0, 22.0], "numinlets": 1, "id": "obj-130"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend append", "numoutlets": 1, "outlettype": [""], "patching_rect": [460.0, 990.0, 90.0, 22.0], "numinlets": 1, "id": "obj-101"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "counter 0 0", "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [520.0, 915.0, 65.0, 22.0], "numinlets": 5, "id": "obj-127"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 0", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [520.0, 940.0, 33.0, 22.0], "numinlets": 2, "id": "obj-128"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend set text", "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 965.0, 86.0, 22.0], "numinlets": 1, "id": "obj-129"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s queue_display", "numoutlets": 0, "patching_rect": [460.0, 820.0, 90.0, 22.0], "numinlets": 1, "id": "obj-59"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r queue_display", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 130.0, 88.0, 22.0], "numinlets": 0, "id": "obj-60"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pack i i i i", "numoutlets": 1, "outlettype": [""], "patching_rect": [320.0, 585.0, 71.0, 22.0], "numinlets": 4, "id": "obj-70"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s matrix_forward_to_live", "numoutlets": 0, "patching_rect": [320.0, 615.0, 130.0, 22.0], "numinlets": 1, "id": "obj-71"}}, {"box": {"maxclass": "comment", "text": "Forward matrix values to Live (normal behavior)", "patching_rect": [460.0, 615.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-72"}}, {"box": {"maxclass": "comment", "text": "9. Actually forward normal matrix values to Live", "patching_rect": [30.0, 650.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-74"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r matrix_forward_to_live", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 680.0, 128.0, 22.0], "numinlets": 0, "id": "obj-75"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "unpack i i i i", "numoutlets": 4, "outlettype": ["int", "int", "int", "int"], "patching_rect": [30.0, 710.0, 71.0, 22.0], "numinlets": 1, "id": "obj-76"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sprintf call send_value %d %d %d", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 740.0, 171.0, 22.0], "numinlets": 3, "id": "obj-77"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 770.0, 62.0, 22.0], "numinlets": 2, "id": "obj-80", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print matrix_forward_send", "numoutlets": 0, "patching_rect": [30.0, 800.0, 135.0, 22.0], "numinlets": 1, "id": "obj-81"}}, {"box": {"maxclass": "comment", "text": "10. Control grabbing to prevent clips launching", "patching_rect": [30.0, 830.0, 280.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-83"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r session_button", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 860.0, 91.0, 22.0], "numinlets": 0, "id": "obj-84"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 1 0", "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [30.0, 890.0, 53.0, 22.0], "numinlets": 1, "id": "obj-85"}}, {"box": {"maxclass": "message", "text": "call grab_control Button_Matrix, call get_control Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 920.0, 350.0, 22.0], "numinlets": 2, "id": "obj-86"}}, {"box": {"maxclass": "message", "text": "call release_control Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [64.0, 920.0, 170.0, 22.0], "numinlets": 2, "id": "obj-87"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 950.0, 62.0], "numinlets": 2, "id": "obj-88", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print matrix_grab_status", "numoutlets": 0, "patching_rect": [30.0, 980.0, 130.0, 22.0], "numinlets": 1, "id": "obj-89"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "coll column_tracker", "numoutlets": 4, "outlettype": ["", "", "", ""], "patching_rect": [400.0, 650.0, 100.0, 22.0], "numinlets": 1, "id": "obj-coltrack"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "js column-filter.js", "numoutlets": 1, "outlettype": [""], "patching_rect": [460.0, 665.0, 95.0, 22.0], "numinlets": 1, "id": "obj-filter"}}, {"box": {"maxclass": "comment", "text": "Play Button Detection", "patching_rect": [680.0, 500.0, 120.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-200"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Play_<PERSON><PERSON>", "numoutlets": 1, "outlettype": [""], "patching_rect": [680.0, 525.0, 190.0, 22.0], "numinlets": 2, "id": "obj-201"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [680.0, 555.0, 60.0, 22.0], "numinlets": 2, "id": "obj-202"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r ---<PERSON><PERSON><PERSON><PERSON>", "numoutlets": 1, "outlettype": [""], "patching_rect": [750.0, 525.0, 75.0, 22.0], "numinlets": 0, "id": "obj-203"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [680.0, 585.0, 140.0, 22.0], "numinlets": 1, "id": "obj-204"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "Push-Observe_A_Control_Value", "numoutlets": 1, "outlettype": [""], "patching_rect": [680.0, 615.0, 170.0, 22.0], "numinlets": 1, "id": "obj-205"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 127", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [680.0, 645.0, 43.0, 22.0], "numinlets": 2, "id": "obj-206"}}, {"box": {"maxclass": "comment", "text": "Clip Queuing System - Uses <PERSON><PERSON>ton's internal queuing via set_fire_button_state", "patching_rect": [680.0, 675.0, 400.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-207"}}, {"box": {"maxclass": "message", "text": "dump", "numoutlets": 1, "outlettype": [""], "patching_rect": [680.0, 700.0, 36.0, 22.0], "numinlets": 2, "id": "obj-208"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "js clipLauncher.js", "numoutlets": 1, "outlettype": [""], "patching_rect": [730.0, 730.0, 95.0, 22.0], "numinlets": 3, "id": "obj-209"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print launcher_result", "numoutlets": 0, "patching_rect": [730.0, 760.0, 105.0, 22.0], "numinlets": 1, "id": "obj-210"}}, {"box": {"maxclass": "message", "text": "clear", "numoutlets": 1, "outlettype": [""], "patching_rect": [780.0, 700.0, 35.0, 22.0], "numinlets": 2, "id": "obj-211"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s launch_clips", "numoutlets": 0, "patching_rect": [730.0, 700.0, 80.0, 22.0], "numinlets": 1, "id": "obj-212"}}, {"box": {"maxclass": "message", "text": "dump", "numoutlets": 1, "outlettype": [""], "patching_rect": [600.0, 730.0, 35.0, 22.0], "numinlets": 2, "id": "obj-213"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route dump", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [580.0, 760.0, 70.0, 22.0], "numinlets": 1, "id": "obj-214"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print launcher_debug", "numoutlets": 0, "patching_rect": [640.0, 790.0, 105.0, 22.0], "numinlets": 1, "id": "obj-215"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "gate", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 740.0, 30.0, 22.0], "numinlets": 2, "id": "obj-216"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t 1 b 0", "numoutlets": 3, "outlettype": ["int", "bang", "int"], "patching_rect": [580.0, 790.0, 40.0, 22.0], "numinlets": 1, "id": "obj-217"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "delay 10", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [620.0, 820.0, 50.0, 22.0], "numinlets": 2, "id": "obj-218"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend dump", "numoutlets": 1, "outlettype": [""], "patching_rect": [460.0, 760.0, 80.0, 22.0], "numinlets": 1, "id": "obj-219"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl.group", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [460.0, 730.0, 50.0, 22.0], "numinlets": 2, "id": "obj-220"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "numoutlets": 1, "outlettype": [""], "patching_rect": [600.0, 760.0, 55.0, 22.0], "numinlets": 1, "id": "obj-302"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print live_path_output", "numoutlets": 0, "patching_rect": [600.0, 790.0, 120.0, 22.0], "numinlets": 1, "id": "obj-314"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [600.0, 790.0, 67.0, 22.0], "numinlets": 2, "id": "obj-311"}}, {"box": {"maxclass": "message", "text": "call set_fire_button_state 1", "numoutlets": 1, "outlettype": [""], "patching_rect": [650.0, 790.0, 160.0, 22.0], "numinlets": 2, "id": "obj-306"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b s", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [600.0, 790.0, 35.0, 22.0], "numinlets": 1, "id": "obj-309"}}, {"box": {"maxclass": "message", "text": "path live_set tracks $1 clip_slots $2", "numoutlets": 1, "outlettype": [""], "patching_rect": [600.0, 850.0, 180.0, 22.0], "numinlets": 2, "id": "obj-325"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print path_message", "numoutlets": 0, "patching_rect": [635.0, 790.0, 100.0, 22.0], "numinlets": 1, "id": "obj-315"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "unpack 0 0", "numoutlets": 2, "outlettype": ["int", "int"], "patching_rect": [600.0, 700.0, 68.0, 22.0], "numinlets": 1, "id": "obj-304"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print unpack_output", "numoutlets": 0, "patching_rect": [600.0, 725.0, 105.0, 22.0], "numinlets": 1, "id": "obj-319"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print unpack_right", "numoutlets": 0, "patching_rect": [680.0, 725.0, 95.0, 22.0], "numinlets": 1, "id": "obj-320"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pack 0 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [600.0, 740.0, 56.0, 22.0], "numinlets": 2, "id": "obj-323"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t l b", "numoutlets": 2, "outlettype": ["", "bang"], "patching_rect": [600.0, 670.0, 35.0, 22.0], "numinlets": 1, "id": "obj-312"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print trigger_output", "numoutlets": 0, "patching_rect": [600.0, 695.0, 105.0, 22.0], "numinlets": 1, "id": "obj-318"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "delay 10", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [650.0, 700.0, 55.0, 22.0], "numinlets": 2, "id": "obj-313"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r sync_launch_clip", "numoutlets": 1, "outlettype": [""], "patching_rect": [600.0, 640.0, 105.0, 22.0], "numinlets": 0, "id": "obj-305"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print receive_output", "numoutlets": 0, "patching_rect": [600.0, 655.0, 105.0, 22.0], "numinlets": 1, "id": "obj-317"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s sync_launch_clip", "numoutlets": 0, "patching_rect": [730.0, 730.0, 105.0, 22.0], "numinlets": 1, "id": "obj-307"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route sync_launch", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [730.0, 700.0, 100.0, 22.0], "numinlets": 1, "id": "obj-308"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print route_output", "numoutlets": 0, "patching_rect": [730.0, 715.0, 95.0, 22.0], "numinlets": 1, "id": "obj-316"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "plugin~", "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [50.0, 550.0, 53.0, 22.0], "numinlets": 2, "id": "obj-300"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "plugout~", "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [50.0, 580.0, 61.0, 22.0], "numinlets": 2, "id": "obj-301"}}], "lines": [{"patchline": {"source": ["obj-300", 0], "destination": ["obj-301", 0]}}, {"patchline": {"source": ["obj-300", 1], "destination": ["obj-301", 1]}}, {"patchline": {"source": ["obj-305", 0], "destination": ["obj-317", 0]}}, {"patchline": {"source": ["obj-305", 0], "destination": ["obj-312", 0]}}, {"patchline": {"source": ["obj-312", 0], "destination": ["obj-318", 0]}}, {"patchline": {"source": ["obj-312", 0], "destination": ["obj-304", 0]}}, {"patchline": {"source": ["obj-312", 0], "destination": ["obj-304", 0]}}, {"patchline": {"source": ["obj-312", 1], "destination": ["obj-313", 0]}}, {"patchline": {"source": ["obj-313", 0], "destination": ["obj-306", 0]}}, {"patchline": {"source": ["obj-304", 0], "destination": ["obj-319", 0]}}, {"patchline": {"source": ["obj-304", 0], "destination": ["obj-323", 0]}}, {"patchline": {"source": ["obj-304", 1], "destination": ["obj-323", 1]}}, {"patchline": {"source": ["obj-323", 0], "destination": ["obj-325", 0]}}, {"patchline": {"source": ["obj-304", 1], "destination": ["obj-320", 0]}}, {"patchline": {"source": ["obj-304", 1], "destination": ["obj-310", 2]}}, {"patchline": {"source": ["obj-325", 0], "destination": ["obj-315", 0]}}, {"patchline": {"source": ["obj-325", 0], "destination": ["obj-302", 0]}}, {"patchline": {"source": ["obj-302", 0], "destination": ["obj-314", 0]}}, {"patchline": {"source": ["obj-302", 0], "destination": ["obj-311", 1]}}, {"patchline": {"source": ["obj-306", 0], "destination": ["obj-311", 0]}}, {"patchline": {"source": ["obj-308", 0], "destination": ["obj-316", 0]}}, {"patchline": {"source": ["obj-308", 0], "destination": ["obj-307", 0]}}, {"patchline": {"source": ["obj-3", 0], "destination": ["obj-4", 0]}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-5", 0]}}, {"patchline": {"source": ["obj-5", 1], "destination": ["obj-6", 0]}}, {"patchline": {"source": ["obj-6", 0], "destination": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-42", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-6", 1]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-57", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-9", 1], "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-23", 1], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-88", 1], "source": ["obj-45", 0]}}, {"patchline": {"source": ["obj-24", 0], "destination": ["obj-80", 1]}}, {"patchline": {"source": ["obj-113", 0], "destination": ["obj-110", 0]}}, {"patchline": {"source": ["obj-110", 0], "destination": ["obj-111", 0]}}, {"patchline": {"source": ["obj-111", 0], "destination": ["obj-112", 0]}}, {"patchline": {"source": ["obj-112", 0], "destination": ["obj-filter", 0]}}, {"patchline": {"source": ["obj-112", 0], "destination": ["obj-59", 0]}}, {"patchline": {"source": ["obj-6", 0], "destination": ["obj-201", 0]}}, {"patchline": {"source": ["obj-201", 0], "destination": ["obj-202", 0]}}, {"patchline": {"source": ["obj-203", 0], "destination": ["obj-202", 1]}}, {"patchline": {"source": ["obj-202", 0], "destination": ["obj-204", 0]}}, {"patchline": {"source": ["obj-204", 0], "destination": ["obj-205", 0]}}, {"patchline": {"source": ["obj-205", 0], "destination": ["obj-206", 0]}}, {"patchline": {"source": ["obj-206", 0], "destination": ["obj-208", 0]}}, {"patchline": {"source": ["obj-206", 0], "destination": ["obj-213", 0]}}, {"patchline": {"source": ["obj-208", 0], "destination": ["obj-95", 0]}}, {"patchline": {"source": ["obj-213", 0], "destination": ["obj-209", 0]}}, {"patchline": {"source": ["obj-209", 0], "destination": ["obj-214", 0]}}, {"patchline": {"source": ["obj-209", 0], "destination": ["obj-308", 0]}}, {"patchline": {"source": ["obj-214", 0], "destination": ["obj-95", 0]}}, {"patchline": {"source": ["obj-95", 0], "destination": ["obj-209", 1]}}, {"patchline": {"source": ["obj-214", 1], "destination": ["obj-210", 0]}}, {"patchline": {"source": ["obj-214", 1], "destination": ["obj-215", 0]}}, {"patchline": {"source": ["obj-211", 0], "destination": ["obj-95", 0]}}, {"patchline": {"source": ["obj-95", 3], "destination": ["obj-209", 2]}}, {"patchline": {"source": ["obj-401", 0], "destination": ["obj-406", 0]}}, {"patchline": {"source": ["obj-404", 0], "destination": ["obj-407", 0]}}, {"patchline": {"source": ["obj-323", 0], "destination": ["obj-470", 0]}}, {"patchline": {"source": ["obj-471", 0], "destination": ["obj-451", 0]}}, {"patchline": {"source": ["obj-452", 0], "destination": ["obj-454", 0]}}, {"patchline": {"source": ["obj-453", 0], "destination": ["obj-455", 0]}}, {"patchline": {"source": ["obj-454", 0], "destination": ["obj-451", 0]}}, {"patchline": {"source": ["obj-455", 0], "destination": ["obj-451", 0]}}, {"patchline": {"source": ["obj-451", 0], "destination": ["obj-456", 0]}}, {"patchline": {"source": ["obj-456", 0], "destination": ["obj-457", 0]}}, {"patchline": {"source": ["obj-457", 0], "destination": ["obj-458", 0]}}, {"patchline": {"source": ["obj-457", 1], "destination": ["obj-458", 1]}}, {"patchline": {"source": ["obj-458", 0], "destination": ["obj-461", 0]}}, {"patchline": {"source": ["obj-461", 0], "destination": ["obj-460", 0]}}, {"patchline": {"source": ["obj-460", 0], "destination": ["obj-459", 1]}}, {"patchline": {"source": ["obj-461", 1], "destination": ["obj-459", 0]}}, {"patchline": {"source": ["obj-459", 0], "destination": ["obj-470", 1]}}, {"patchline": {"source": ["obj-459", 1], "destination": ["obj-470", 0]}}, {"patchline": {"source": ["obj-470", 0], "destination": ["obj-471", 0]}}, {"patchline": {"source": ["obj-470", 1], "destination": ["obj-471", 1]}}, {"patchline": {"source": ["obj-471", 0], "destination": ["obj-406", 0]}}, {"patchline": {"source": ["obj-471", 1], "destination": ["obj-406", 1]}}, {"patchline": {"source": ["obj-406", 0], "destination": ["obj-407", 0]}}, {"patchline": {"source": ["obj-406", 1], "destination": ["obj-407", 1]}}, {"patchline": {"source": ["obj-407", 0], "destination": ["obj-451", 0]}}, {"patchline": {"source": ["obj-407", 1], "destination": ["obj-451", 1]}}], "dependency_cache": [], "autosave": 0}}