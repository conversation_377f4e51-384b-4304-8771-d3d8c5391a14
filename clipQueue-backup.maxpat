{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 87.0, 800.0, 600.0], "bglocked": 0, "openinpresentation": 1, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"maxclass": "comment", "text": "Push 2 Clip <PERSON>", "presentation_rect": [10.0, 10.0, 250.0, 25.0], "presentation": 1, "patching_rect": [30.0, 30.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 18.0, "fontface": 1, "id": "obj-1"}}, {"box": {"maxclass": "comment", "text": "Hold Session button and press pads to queue clips", "presentation_rect": [10.0, 40.0, 300.0, 20.0], "presentation": 1, "patching_rect": [30.0, 50.0, 300.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 11.0, "id": "obj-46"}}, {"box": {"maxclass": "comment", "text": "Status:", "presentation_rect": [10.0, 70.0, 50.0, 20.0], "presentation": 1, "patching_rect": [30.0, 70.0, 50.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-47"}}, {"box": {"maxclass": "textedit", "text": "Initializing...", "presentation_rect": [65.0, 70.0, 200.0, 20.0], "presentation": 1, "patching_rect": [85.0, 70.0, 200.0, 20.0], "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", ""], "readonly": 1, "id": "obj-48"}}, {"box": {"maxclass": "comment", "text": "Session Button:", "presentation_rect": [10.0, 100.0, 100.0, 20.0], "presentation": 1, "patching_rect": [30.0, 100.0, 100.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-49"}}, {"box": {"maxclass": "led", "presentation_rect": [115.0, 102.0, 16.0, 16.0], "presentation": 1, "patching_rect": [135.0, 102.0, 16.0, 16.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "id": "obj-50"}}, {"box": {"maxclass": "comment", "text": "Queued Clips:", "presentation_rect": [10.0, 130.0, 100.0, 20.0], "presentation": 1, "patching_rect": [30.0, 130.0, 100.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-51"}}, {"box": {"maxclass": "textedit", "presentation_rect": [10.0, 155.0, 300.0, 150.0], "presentation": 1, "patching_rect": [30.0, 155.0, 300.0, 150.0], "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", ""], "text": "No clips queued", "readonly": 1, "id": "obj-52"}}, {"box": {"maxclass": "comment", "text": "1. Initialize Push2 connection", "patching_rect": [30.0, 90.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-2"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadbang", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [30.0, 120.0, 58.0, 22.0], "numinlets": 1, "id": "obj-3"}}, {"box": {"maxclass": "message", "text": "path control_surfaces 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 150.0, 127.0, 22.0], "numinlets": 2, "id": "obj-4"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "saved_object_attributes": {"_persistence": 1, "parameter_enable": 1}, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [30.0, 180.0, 67.0, 22.0], "id": "obj-5", "color": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [30.0, 210.0, 32.5, 22.0], "numinlets": 1, "id": "obj-6"}}, {"box": {"id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [70.0, 210.0, 76.0, 22.0], "text": "s ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [100.0, 270.0, 74.0, 22.0], "text": "r ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-45", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [470.0, 120.0, 74.0, 22.0], "text": "r ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"maxclass": "comment", "text": "2. Get Session button control", "patching_rect": [30.0, 240.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-7"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Session_Mode_Button", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 270.0, 245.0, 22.0], "numinlets": 2, "id": "obj-8"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 300.0, 62.0, 22.0], "numinlets": 2, "id": "obj-9", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 330.0, 155.0, 22.0], "numinlets": 1, "id": "obj-10"}}, {"box": {"id": "obj-37", "maxclass": "print", "numinlets": 1, "numoutlets": 0, "patching_rect": [30.0, 355.0, 125.0, 22.0], "text": "session_button_id"}}, {"box": {"maxclass": "comment", "text": "3. Observe button state", "patching_rect": [30.0, 360.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-11"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [30.0, 390.0, 32.5, 22.0], "numinlets": 1, "id": "obj-12"}}, {"box": {"maxclass": "message", "text": "property value", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 420.0, 79.0, 22.0], "numinlets": 2, "id": "obj-13"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.observer", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 450.0, 72.0, 22.0], "numinlets": 2, "id": "obj-14", "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "comment", "text": "4. <PERSON>le button state", "patching_rect": [30.0, 480.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-15"}}, {"box": {"maxclass": "comment", "text": "Convert button values: 127->1 (pressed), 0->0 (released)", "patching_rect": [90.0, 510.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-20"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 127 0", "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [30.0, 510.0, 53.0, 22.0], "numinlets": 1, "id": "obj-16"}}, {"box": {"maxclass": "message", "text": "1", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 540.0, 32.0, 22.0], "numinlets": 2, "id": "obj-17"}}, {"box": {"maxclass": "message", "text": "0", "numoutlets": 1, "outlettype": [""], "patching_rect": [64.0, 540.0, 32.0, 22.0], "numinlets": 2, "id": "obj-18"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s session_button", "numoutlets": 0, "patching_rect": [30.0, 570.0, 93.0, 22.0], "numinlets": 1, "id": "obj-19"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print session_button_send", "numoutlets": 0, "patching_rect": [30.0, 600.0, 140.0, 22.0], "numinlets": 1, "id": "obj-30"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s session_led", "numoutlets": 0, "patching_rect": [120.0, 570.0, 78.0, 22.0], "numinlets": 1, "id": "obj-53"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r session_led", "numoutlets": 1, "outlettype": [""], "patching_rect": [135.0, 80.0, 76.0, 22.0], "numinlets": 0, "id": "obj-54"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s status_update", "numoutlets": 0, "patching_rect": [250.0, 180.0, 88.0, 22.0], "numinlets": 1, "id": "obj-55"}}, {"box": {"maxclass": "message", "text": "set text Push 2 Connected", "numoutlets": 1, "outlettype": [""], "patching_rect": [250.0, 150.0, 125.0, 22.0], "numinlets": 2, "id": "obj-57"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r status_update", "numoutlets": 1, "outlettype": [""], "patching_rect": [85.0, 50.0, 86.0, 22.0], "numinlets": 0, "id": "obj-56"}}, {"box": {"maxclass": "comment", "text": "5. <PERSON> Button Matrix", "patching_rect": [400.0, 90.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-21"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 120.0, 200.0, 22.0], "numinlets": 2, "id": "obj-22"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 150.0, 62.0, 22.0], "numinlets": 2, "id": "obj-23", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [400.0, 180.0, 155.0, 22.0], "numinlets": 1, "id": "obj-24"}}, {"box": {"id": "obj-38", "maxclass": "print", "numinlets": 1, "numoutlets": 0, "patching_rect": [500.0, 180.0, 108.0, 22.0], "text": "matrix_button_id"}}, {"box": {"id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 330.0, 91.0, 22.0], "text": "r session_button"}}, {"box": {"id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [520.0, 360.0, 150.0, 22.0], "text": "Push-Release_Grab_A_Control"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print control_grab_status", "numoutlets": 0, "patching_rect": [520.0, 390.0, 135.0, 22.0], "numinlets": 1, "id": "obj-61"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 1 0", "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [520.0, 390.0, 53.0, 22.0], "numinlets": 1, "id": "obj-68"}}, {"box": {"maxclass": "message", "text": "call grab_control $1", "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 420.0, 110.0, 22.0], "numinlets": 2, "id": "obj-69"}}, {"box": {"maxclass": "message", "text": "call release_control $1", "numoutlets": 1, "outlettype": [""], "patching_rect": [555.0, 450.0, 125.0, 22.0], "numinlets": 2, "id": "obj-70"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 480.0, 62.0], "numinlets": 2, "id": "obj-71", "saved_object_attributes": {"_persistence": 1}}}, {"patchline": {"destination": ["obj-68", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-69", 0], "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-70", 0], "source": ["obj-68", 1]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-71", 1], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-69", 1], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-70", 1], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-61", 0], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-57", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-6", 1]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-9", 1], "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-10", 0], "source": ["obj-9", 0]}}, {"patchline": {"destination": ["obj-12", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-37", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-14", 1], "source": ["obj-12", 1]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-16", 1]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-23", 1], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-24", 1]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-54", 0]}}, {"box": {"maxclass": "comment", "text": "Push 2 Clip <PERSON>", "presentation_rect": [10.0, 10.0, 250.0, 25.0], "presentation": 1, "patching_rect": [30.0, 30.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 18.0, "fontface": 1, "id": "obj-1"}}, {"box": {"maxclass": "comment", "text": "Hold Session button and press pads to queue clips", "presentation_rect": [10.0, 40.0, 300.0, 20.0], "presentation": 1, "patching_rect": [30.0, 50.0, 300.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontsize": 11.0, "id": "obj-46"}}, {"box": {"maxclass": "comment", "text": "Status:", "presentation_rect": [10.0, 70.0, 50.0, 20.0], "presentation": 1, "patching_rect": [30.0, 70.0, 50.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-47"}}, {"box": {"maxclass": "textedit", "text": "Initializing...", "presentation_rect": [65.0, 70.0, 200.0, 20.0], "presentation": 1, "patching_rect": [85.0, 70.0, 200.0, 20.0], "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", ""], "readonly": 1, "id": "obj-48"}}, {"box": {"maxclass": "comment", "text": "Session Button:", "presentation_rect": [10.0, 100.0, 100.0, 20.0], "presentation": 1, "patching_rect": [30.0, 100.0, 100.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-49"}}, {"box": {"maxclass": "led", "presentation_rect": [115.0, 102.0, 16.0, 16.0], "presentation": 1, "patching_rect": [135.0, 102.0, 16.0, 16.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "id": "obj-50"}}, {"box": {"maxclass": "comment", "text": "Queued Clips:", "presentation_rect": [10.0, 130.0, 100.0, 20.0], "presentation": 1, "patching_rect": [30.0, 130.0, 100.0, 20.0], "numinlets": 1, "numoutlets": 0, "fontface": 1, "id": "obj-51"}}, {"box": {"maxclass": "textedit", "presentation_rect": [10.0, 155.0, 300.0, 150.0], "presentation": 1, "patching_rect": [30.0, 155.0, 300.0, 150.0], "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", ""], "text": "No clips queued", "readonly": 1, "id": "obj-52"}}, {"box": {"maxclass": "comment", "text": "1. Initialize Push2 connection", "patching_rect": [30.0, 90.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-2"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadbang", "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [30.0, 120.0, 58.0, 22.0], "numinlets": 1, "id": "obj-3"}}, {"box": {"maxclass": "message", "text": "path control_surfaces 0", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 150.0, 127.0, 22.0], "numinlets": 2, "id": "obj-4"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "saved_object_attributes": {"_persistence": 1, "parameter_enable": 1}, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [30.0, 180.0, 67.0, 22.0], "id": "obj-5", "color": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [30.0, 210.0, 32.5, 22.0], "numinlets": 1, "id": "obj-6"}}, {"box": {"id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [70.0, 210.0, 76.0, 22.0], "text": "s ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [100.0, 270.0, 74.0, 22.0], "text": "r ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-45", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [470.0, 120.0, 74.0, 22.0], "text": "r ---<PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"maxclass": "comment", "text": "2. Get Session button control", "patching_rect": [30.0, 240.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-7"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Session_Mode_Button", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 270.0, 245.0, 22.0], "numinlets": 2, "id": "obj-8"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 300.0, 62.0, 22.0], "numinlets": 2, "id": "obj-9", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 330.0, 155.0, 22.0], "numinlets": 1, "id": "obj-10"}}, {"box": {"id": "obj-37", "maxclass": "print", "numinlets": 1, "numoutlets": 0, "patching_rect": [30.0, 355.0, 125.0, 22.0], "text": "session_button_id"}}, {"box": {"maxclass": "comment", "text": "3. Observe button state", "patching_rect": [30.0, 360.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-11"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [30.0, 390.0, 32.5, 22.0], "numinlets": 1, "id": "obj-12"}}, {"box": {"maxclass": "message", "text": "property value", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 420.0, 79.0, 22.0], "numinlets": 2, "id": "obj-13"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.observer", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [30.0, 450.0, 72.0, 22.0], "numinlets": 2, "id": "obj-14", "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "comment", "text": "4. <PERSON>le button state", "patching_rect": [30.0, 480.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-15"}}, {"box": {"maxclass": "comment", "text": "Convert button values: 127->1 (pressed), 0->0 (released)", "patching_rect": [90.0, 510.0, 250.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-20"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 127 0", "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [30.0, 510.0, 53.0, 22.0], "numinlets": 1, "id": "obj-16"}}, {"box": {"maxclass": "message", "text": "1", "numoutlets": 1, "outlettype": [""], "patching_rect": [30.0, 540.0, 32.0, 22.0], "numinlets": 2, "id": "obj-17"}}, {"box": {"maxclass": "message", "text": "0", "numoutlets": 1, "outlettype": [""], "patching_rect": [64.0, 540.0, 32.0, 22.0], "numinlets": 2, "id": "obj-18"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s session_button", "numoutlets": 0, "patching_rect": [30.0, 570.0, 93.0, 22.0], "numinlets": 1, "id": "obj-19"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print session_button_send", "numoutlets": 0, "patching_rect": [30.0, 600.0, 140.0, 22.0], "numinlets": 1, "id": "obj-30"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s session_led", "numoutlets": 0, "patching_rect": [120.0, 570.0, 78.0, 22.0], "numinlets": 1, "id": "obj-53"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r session_led", "numoutlets": 1, "outlettype": [""], "patching_rect": [135.0, 80.0, 76.0, 22.0], "numinlets": 0, "id": "obj-54"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "s status_update", "numoutlets": 0, "patching_rect": [250.0, 180.0, 88.0, 22.0], "numinlets": 1, "id": "obj-55"}}, {"box": {"maxclass": "message", "text": "set text Push 2 Connected", "numoutlets": 1, "outlettype": [""], "patching_rect": [250.0, 150.0, 125.0, 22.0], "numinlets": 2, "id": "obj-57"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "r status_update", "numoutlets": 1, "outlettype": [""], "patching_rect": [85.0, 50.0, 86.0, 22.0], "numinlets": 0, "id": "obj-56"}}, {"box": {"maxclass": "comment", "text": "5. <PERSON> Button Matrix", "patching_rect": [400.0, 90.0, 150.0, 20.0], "numinlets": 1, "numoutlets": 0, "id": "obj-21"}}, {"box": {"maxclass": "message", "text": "call get_control_by_name Button_Matrix", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 120.0, 200.0, 22.0], "numinlets": 2, "id": "obj-22"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [400.0, 150.0, 62.0, 22.0], "numinlets": 2, "id": "obj-23", "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route get_control_by_name", "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [400.0, 180.0, 155.0, 22.0], "numinlets": 1, "id": "obj-24"}}, {"box": {"id": "obj-38", "maxclass": "print", "numinlets": 1, "numoutlets": 0, "patching_rect": [500.0, 180.0, 108.0, 22.0], "text": "matrix_button_id"}}, {"box": {"id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 330.0, 91.0, 22.0], "text": "r session_button"}}, {"box": {"id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [520.0, 360.0, 150.0, 22.0], "text": "Push-Release_Grab_A_Control"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print control_grab_status", "numoutlets": 0, "patching_rect": [520.0, 390.0, 135.0, 22.0], "numinlets": 1, "id": "obj-61"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 1 0", "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [520.0, 390.0, 53.0, 22.0], "numinlets": 1, "id": "obj-68"}}, {"box": {"maxclass": "message", "text": "call grab_control $1", "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 420.0, 110.0, 22.0], "numinlets": 2, "id": "obj-69"}}, {"box": {"maxclass": "message", "text": "call release_control $1", "numoutlets": 1, "outlettype": [""], "patching_rect": [555.0, 450.0, 125.0, 22.0], "numinlets": 2, "id": "obj-70"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "outlettype": [""], "patching_rect": [520.0, 480.0, 62.0], "numinlets": 2, "id": "obj-71", "saved_object_attributes": {"_persistence": 1}}}]}}