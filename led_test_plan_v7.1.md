# LED Control v7.1 - Push2 send_value Method - June 27, 2025

## 🎯 **BREAKTHROUGH: Using Push2-ArrangeMode Method!**

### ✅ **Key Discovery:**
The Push2-ArrangeMode project uses `call send_value` through `live.object`, NOT raw MIDI `noteout`!

### ✅ **Updated Implementation v7.1:**

**JavaScript (clipLauncher.js v7.1):**
- ✅ **Changed to Push2 method:** `outlet(0, "call", "send_value", scene, track, velocity)`
- ✅ **Coordinates:** Using scene/track coordinates (0-7, 0-7) instead of MIDI notes  
- ✅ **Continuous updates:** 100ms refresh to maintain LED control
- ✅ **Proper cleanup:** LEDs turned off on session exit

**Max Patch (clipQueue.maxpat):**
- ✅ **Updated routing:** `route led_debug call` instead of `led_control`
- ✅ **Direct connection:** `call` messages → existing `live.object` (obj-88)
- ✅ **Removed noteout:** No more MIDI approach, using Push2's API
- ✅ **Debug monitoring:** `print MIDI_OUT` shows send_value commands

### 🧪 **Expected Test Results:**

**When holding Session button:**
```
LED_DEBUG: session_enter
LED_DEBUG: clip_found 0 0 36
MIDI_OUT: call send_value 0 0 126    (scene 0, track 0, green)
MIDI_OUT: call send_value 0 1 127    (scene 0, track 1, red if playing)
MIDI_OUT: call send_value 1 0 126    (scene 1, track 0, green)
MIDI_OUT: call send_value 1 1 126    (scene 1, track 1, green)
...continuous updates every 100ms...
```

**Physical Result:** Bottom 2×2 pads should light up and STAY lit

**When releasing Session button:**
```
LED_DEBUG: session_exit
MIDI_OUT: call send_value 0 0 0      (turn off scene 0, track 0)
MIDI_OUT: call send_value 0 1 0      (turn off scene 0, track 1)
MIDI_OUT: call send_value 1 0 0      (turn off scene 1, track 0)
MIDI_OUT: call send_value 1 1 0      (turn off scene 1, track 1)
```

**Physical Result:** Test area LEDs should turn off

### 🔧 **Push2 send_value API:**

**Format:** `call send_value row column velocity`
- **row:** Scene index (0-7, top to bottom)
- **column:** Track index (0-7, left to right)  
- **velocity:** 0=off, 1-127=colors (126=green, 127=red/bright)

This matches exactly how Live's Push2 interface controls the LED matrix!

### 📊 **Key Differences from Raw MIDI:**

| Method | Old (noteout) | New (send_value) |
|--------|---------------|------------------|
| **Target** | MIDI device | Push2 live.object |
| **Format** | note + velocity | row + column + velocity |
| **Coordinates** | MIDI notes 36-99 | Scene/track 0-7 |
| **Connection** | External MIDI | Internal Live API |

### 🚀 **Why This Should Work:**

1. **Same method** as working Push2-ArrangeMode project
2. **Internal API** instead of external MIDI routing
3. **Proper coordinates** that Push2 understands
4. **Existing live.object** already configured for Push2

**Status: Ready for Push2 LED API testing!** 🎛️✨
