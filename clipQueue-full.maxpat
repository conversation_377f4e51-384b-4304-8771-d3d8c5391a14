{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 87.0, 1372.0, 779.0], "bglocked": 0, "openinpresentation": 1, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "plugin~", "numoutlets": 2, "id": "obj-1", "outlettype": ["signal", "signal"], "patching_rect": [30.0, 400.0, 74.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "plugout~", "numoutlets": 2, "id": "obj-2", "outlettype": ["signal", "signal"], "patching_rect": [30.0, 350.0, 74.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "comment", "text": "Push 2 Clip <PERSON>", "presentation_rect": [10.0, 10.0, 200.0, 18.0], "numoutlets": 0, "id": "obj-3", "presentation": 1, "patching_rect": [30.0, 30.0, 200.0, 18.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadbang", "numoutlets": 1, "id": "obj-4", "outlettype": ["bang"], "patching_rect": [30.0, 60.0, 58.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "message", "text": "path this_device", "numoutlets": 1, "id": "obj-5", "outlettype": [""], "patching_rect": [30.0, 90.0, 143.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "thispatcher", "numoutlets": 2, "id": "obj-6", "outlettype": ["", ""], "patching_rect": [30.0, 120.0, 69.0, 20.0], "numinlets": 1, "save": ["#N", "thispatcher", ";", "#Q", "end", ";"]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadmess poll", "numoutlets": 1, "id": "obj-7", "outlettype": [""], "patching_rect": [30.0, 150.0, 85.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path live_set", "numoutlets": 3, "id": "obj-8", "outlettype": ["", "", ""], "patching_rect": [30.0, 180.0, 111.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "id": "obj-9", "outlettype": [""], "patching_rect": [30.0, 210.0, 57.0, 20.0], "numinlets": 2, "saved_object_attributes": {"_persistence": 1}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route path", "numoutlets": 2, "id": "obj-10", "outlettype": ["", ""], "patching_rect": [150.0, 90.0, 62.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "mid<PERSON><PERSON>e", "numoutlets": 8, "id": "obj-11", "outlettype": ["", "", "", "int", "int", "", "int", ""], "patching_rect": [150.0, 120.0, 92.5, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "unpack 0 0", "numoutlets": 2, "id": "obj-12", "outlettype": ["int", "int"], "patching_rect": [150.0, 180.0, 65.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "toggle", "numoutlets": 1, "parameter_enable": 0, "id": "obj-13", "outlettype": ["int"], "patching_rect": [150.0, 150.0, 24.0, 24.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "gate", "numoutlets": 1, "id": "obj-14", "outlettype": [""], "patching_rect": [200.0, 180.0, 34.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pack 0 0", "numoutlets": 1, "id": "obj-15", "outlettype": ["list"], "patching_rect": [150.0, 210.0, 54.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "message", "text": "set $1 $2", "numoutlets": 1, "id": "obj-16", "outlettype": [""], "patching_rect": [150.0, 240.0, 54.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadmess 51", "numoutlets": 1, "id": "obj-17", "outlettype": [""], "patching_rect": [300.0, 90.0, 70.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "Push-Get_A_Control_id", "numoutlets": 2, "id": "obj-18", "outlettype": ["", ""], "patching_rect": [300.0, 120.0, 133.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "Push-Observe_A_Control_Value", "numoutlets": 1, "id": "obj-19", "outlettype": [""], "patching_rect": [300.0, 150.0, 162.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel 1", "numoutlets": 2, "id": "obj-20", "outlettype": ["bang", ""], "patching_rect": [300.0, 180.0, 36.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "message", "text": "Play", "numoutlets": 1, "id": "obj-21", "outlettype": [""], "patching_rect": [300.0, 210.0, 32.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "coll clipQueue", "numoutlets": 4, "id": "obj-22", "outlettype": ["", "", "", ""], "patching_rect": [300.0, 240.0, 133.0, 20.0], "numinlets": 1, "saved_object_attributes": {"embed": 0}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "Push-Observe_A_Control_Value", "numoutlets": 1, "id": "obj-23", "outlettype": [""], "patching_rect": [300.0, 270.0, 162.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "counter", "numoutlets": 4, "id": "obj-24", "outlettype": ["int", "", "", "int"], "patching_rect": [300.0, 300.0, 73.0, 20.0], "numinlets": 5}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "numoutlets": 1, "id": "obj-25", "outlettype": [""], "patching_rect": [300.0, 330.0, 57.0, 20.0], "numinlets": 2, "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "comment", "text": "Queued Clips: 0", "presentation_rect": [10.0, 40.0, 150.0, 18.0], "numoutlets": 0, "id": "obj-26", "presentation": 1, "patching_rect": [30.0, 270.0, 150.0, 18.0], "numinlets": 1}}, {"box": {"maxclass": "live.text", "varname": "live.text", "presentation_rect": [10.0, 70.0, 100.0, 20.0], "numoutlets": 2, "parameter_enable": 1, "id": "obj-27", "outlettype": ["", ""], "presentation": 1, "text": "Clear Queue", "mode": 0, "patching_rect": [30.0, 300.0, 44.0, 15.0], "numinlets": 1, "saved_attribute_attributes": {"valueof": {"parameter_enum": ["val1", "val2"], "parameter_longname": "live.text", "parameter_mmax": 1, "parameter_shortname": "live.text", "parameter_type": 2}}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sprintf set Queued Clips: %d", "numoutlets": 1, "id": "obj-100", "outlettype": [""], "patching_rect": [400.0, 120.0, 140.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "numoutlets": 3, "id": "obj-101", "outlettype": ["", "", ""], "patching_rect": [400.0, 90.0, 100.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "gate", "numoutlets": 1, "id": "obj-102", "outlettype": [""], "patching_rect": [450.0, 150.0, 35.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "message", "text": "clear", "numoutlets": 1, "id": "obj-103", "outlettype": [""], "patching_rect": [450.0, 180.0, 35.0, 20.0], "numinlets": 2}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b", "numoutlets": 1, "id": "obj-104", "outlettype": ["bang"], "patching_rect": [450.0, 210.0, 27.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print Session_Button_State", "numoutlets": 0, "id": "obj-108", "patching_rect": [300.0, 180.0, 150.0, 20.0], "numinlets": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "print Session_Button_State", "numoutlets": 0, "id": "obj-108", "patching_rect": [300.0, 180.0, 150.0, 20.0], "numinlets": 1}}], "lines": [{"patchline": {"source": ["obj-15", 0], "destination": ["obj-22", 0]}}, {"patchline": {"source": ["obj-17", 0], "destination": ["obj-18", 0]}}, {"patchline": {"source": ["obj-19", 0], "destination": ["obj-108", 0]}}, {"patchline": {"source": ["obj-105", 1], "destination": ["obj-106", 0]}}, {"patchline": {"source": ["obj-106", 1], "destination": ["obj-107", 0]}}, {"patchline": {"source": ["obj-102", 0], "destination": ["obj-22", 0]}}, {"patchline": {"source": ["obj-22", 1], "destination": ["obj-100", 0]}}, {"patchline": {"source": ["obj-19", 0], "destination": ["obj-102", 1]}}, {"patchline": {"source": ["obj-21", 0], "destination": ["obj-104", 0]}}, {"patchline": {"source": ["obj-22", 0], "destination": ["obj-23", 0]}}, {"patchline": {"source": ["obj-23", 0], "destination": ["obj-105", 0]}}, {"patchline": {"source": ["obj-24", 0], "destination": ["obj-25", 0]}}, {"patchline": {"source": ["obj-20", 0], "destination": ["obj-21", 0]}}, {"patchline": {"source": ["obj-13", 0], "destination": ["obj-14", 0]}}, {"patchline": {"source": ["obj-12", 0], "destination": ["obj-13", 0]}}, {"patchline": {"source": ["obj-11", 0], "destination": ["obj-15", 0]}}, {"patchline": {"source": ["obj-10", 0], "destination": ["obj-11", 0]}}, {"patchline": {"source": ["obj-101", 0], "destination": ["obj-100", 0], "hidden": 0, "midpoints": []}}]}}