<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>19H2</string>
	<key>C74ObjectProperties</key>
	<dict>
		<key>c74excludefromcollectives</key>
		<string></string>
	</dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleExecutable</key>
	<string>11strokes</string>
	<key>CFBundleIdentifier</key>
	<string>.</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLongVersionString</key>
	<string>11strokes  - </string>
	<key>CFBundlePackageType</key>
	<string>iLaX</string>
	<key>CFBundleSignature</key>
	<string>max2</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CSResourcesFileMapped</key>
	<true/>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>12D4e</string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>11.1</string>
	<key>DTSDKBuild</key>
	<string>20C63</string>
	<key>DTSDKName</key>
	<string>macosx11.1</string>
	<key>DTXcode</key>
	<string>1240</string>
	<key>DTXcodeBuild</key>
	<string>12D4e</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.11</string>
	<key>LSRequiresCarbon</key>
	<true/>
	<key>NSHumanReadableCopyright</key>
	<string></string>
</dict>
</plist>
