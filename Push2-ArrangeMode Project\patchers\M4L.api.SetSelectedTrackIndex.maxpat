{"patcher": {"fileversion": 1, "rect": [86.0, 44.0, 550.0, 588.0], "bglocked": 0, "defrect": [86.0, 44.0, 550.0, 588.0], "openrect": [0.0, 0.0, 0.0, 0.0], "openinpresentation": 0, "default_fontsize": 10.0, "default_fontface": 0, "default_fontname": "Arial Bold", "gridonopen": 0, "gridsize": [8.0, 8.0], "gridsnaponopen": 0, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "boxes": [{"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route bang <empty>", "outlettype": ["", "", ""], "patching_rect": [192.0, 392.0, 106.0, 18.0], "id": "obj-19", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 3, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "< this live.object points to the current Live Set's main view - now we set the property \"selected_track\" to the given id", "linecount": 3, "patching_rect": [328.0, 480.0, 187.0, 41.0], "id": "obj-17", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "help live.object", "outlettype": [""], "patching_rect": [328.0, 520.0, 83.0, 16.0], "id": "obj-18", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "bgcolor": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pcontrol", "outlettype": [""], "patching_rect": [328.0, 544.0, 50.0, 18.0], "hidden": 1, "id": "obj-20", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "--->", "patching_rect": [112.0, 320.0, 27.0, 18.0], "id": "obj-25", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "after the live.object points to the current Live Set we ask it for a list of currently visible tracks (NOT including return tracks)", "linecount": 7, "patching_rect": [16.0, 320.0, 108.0, 87.0], "id": "obj-24", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "--->", "patching_rect": [144.0, 240.0, 27.0, 18.0], "id": "obj-23", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "we need a list of visible tracks so we let the live.path point to the current Live Set and feed the live.object below with its ID", "linecount": 5, "patching_rect": [16.0, 240.0, 138.0, 64.0], "id": "obj-53", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "text": "< first we set the live.path to the main view of the current Live Set and feed the live.object below with its ID", "linecount": 4, "patching_rect": [352.0, 160.0, 169.0, 52.0], "id": "obj-26", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "help live.path", "outlettype": [""], "patching_rect": [352.0, 208.0, 74.0, 16.0], "id": "obj-52", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "bgcolor": [0.984314, 0.819608, 0.05098, 1.0]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pcontrol", "outlettype": [""], "patching_rect": [352.0, 232.0, 50.0, 18.0], "hidden": 1, "id": "obj-51", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "comment", "prototypename": "ML.subpatcher-title", "text": "Set Selected Track Index", "patching_rect": [16.0, 16.0, 295.0, 34.0], "id": "obj-48", "frgb": [0.3, 0.34, 0.4, 1.0], "fontname": "Arial Bold Italic", "numinlets": 1, "textcolor": [0.3, 0.34, 0.4, 1.0], "numoutlets": 0, "fontsize": 24.0}}, {"box": {"maxclass": "comment", "prototypename": "<PERSON><PERSON><PERSON>patcher-story", "text": "Set the selected track to a given index. The index will be mapped against the list of visible tracks.", "linecount": 2, "patching_rect": [16.0, 48.0, 320.0, 32.0], "id": "obj-50", "frgb": [0.101961, 0.121569, 0.172549, 1.0], "fontname": "Arial Italic", "numinlets": 1, "numoutlets": 0, "fontsize": 11.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "int", "outlettype": ["int"], "patching_rect": [320.0, 368.0, 35.0, 18.0], "id": "obj-16", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b b l", "outlettype": ["bang", "bang", ""], "patching_rect": [272.0, 208.0, 46.0, 18.0], "id": "obj-15", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 3, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b l", "outlettype": ["bang", ""], "patching_rect": [216.0, 288.0, 37.0, 18.0], "id": "obj-14", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t b i", "outlettype": ["bang", "int"], "patching_rect": [320.0, 128.0, 35.0, 18.0], "id": "obj-10", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "path live_set view", "outlettype": [""], "patching_rect": [248.0, 160.0, 96.0, 16.0], "id": "obj-11", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "outlettype": [""], "color": [0.984314, 0.819608, 0.05098, 1.0], "patching_rect": [256.0, 504.0, 61.0, 18.0], "id": "obj-12", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "outlettype": ["", "", ""], "color": [0.984314, 0.819608, 0.05098, 1.0], "patching_rect": [248.0, 184.0, 66.0, 18.0], "id": "obj-13", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 3, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "prepend set selected_track id", "outlettype": [""], "patching_rect": [144.0, 472.0, 152.0, 18.0], "id": "obj-9", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl mth", "outlettype": ["", ""], "patching_rect": [232.0, 440.0, 40.0, 18.0], "id": "obj-8", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "zl delace", "outlettype": ["", ""], "patching_rect": [192.0, 416.0, 59.0, 18.0], "id": "obj-7", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "route visible_tracks", "outlettype": ["", ""], "patching_rect": [192.0, 368.0, 104.0, 18.0], "id": "obj-6", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "get visible_tracks", "outlettype": [""], "patching_rect": [136.0, 320.0, 95.0, 16.0], "id": "obj-5", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "message", "text": "path live_set", "outlettype": [""], "patching_rect": [192.0, 240.0, 71.0, 16.0], "id": "obj-4", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.object", "outlettype": [""], "color": [0.984314, 0.819608, 0.05098, 1.0], "patching_rect": [192.0, 344.0, 61.0, 18.0], "id": "obj-3", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "saved_object_attributes": {"_persistence": 0}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "live.path", "outlettype": ["", "", ""], "color": [0.984314, 0.819608, 0.05098, 1.0], "patching_rect": [192.0, 264.0, 67.0, 18.0], "id": "obj-2", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 3, "fontsize": 10.0}}, {"box": {"maxclass": "inlet", "outlettype": [""], "patching_rect": [320.0, 96.0, 18.0, 18.0], "id": "obj-1", "numinlets": 0, "numoutlets": 1, "comment": ""}}], "lines": [{"patchline": {"source": ["obj-6", 0], "destination": ["obj-19", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-19", 2], "destination": ["obj-7", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-3", 0], "destination": ["obj-6", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-9", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-2", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-5", 0], "destination": ["obj-3", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-11", 0], "destination": ["obj-13", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-1", 0], "destination": ["obj-10", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-10", 1], "destination": ["obj-16", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-10", 0], "destination": ["obj-11", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-9", 0], "destination": ["obj-12", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-16", 0], "destination": ["obj-8", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-13", 1], "destination": ["obj-15", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-15", 2], "destination": ["obj-12", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-15", 1], "destination": ["obj-16", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-15", 0], "destination": ["obj-4", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-2", 1], "destination": ["obj-14", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-14", 0], "destination": ["obj-5", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-14", 1], "destination": ["obj-3", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-7", 1], "destination": ["obj-8", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-52", 0], "destination": ["obj-51", 0], "hidden": 1, "midpoints": []}}, {"patchline": {"source": ["obj-18", 0], "destination": ["obj-20", 0], "hidden": 1, "midpoints": []}}]}}