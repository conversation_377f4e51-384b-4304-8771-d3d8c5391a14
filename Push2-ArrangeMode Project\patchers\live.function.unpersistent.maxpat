{"patcher": {"fileversion": 1, "appversion": {"major": 7, "minor": 0, "revision": 5, "architecture": "x64", "modernui": 1}, "rect": [37.0, 79.0, 935.0, 499.0], "bglocked": 0, "openinpresentation": 0, "default_fontsize": 10.0, "default_fontface": 0, "default_fontname": "Arial Bold Italic", "gridonopen": 1, "gridsize": [8.0, 8.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "boxes": [{"box": {"fontname": "Arial Bold Italic", "fontsize": 11.0, "id": "obj-10", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [536.0, 48.0, 347.0, 19.0], "style": "", "text": "Simplified control of Live object functions using the Live API"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-9", "linecount": 2, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [776.0, 408.0, 120.0, 29.0], "style": "", "text": "ID and path data to be passed to other objects"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-6", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [216.0, 400.0, 126.0, 18.0], "style": "", "text": "Messages from live.object"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-7", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [216.0, 344.0, 114.0, 18.0], "style": "", "text": "Messages to live.object"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-2", "linecount": 3, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [176.0, 160.0, 92.0, 40.0], "style": "", "text": "Sends a bang when all attributes are loaded"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-5", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [776.0, 272.0, 62.0, 20.0], "style": "", "text": "prepend id"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [496.0, 160.0, 67.0, 20.0], "style": "", "text": "substitute 0"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [568.0, 160.0, 93.0, 20.0], "style": "", "text": "substitute bang 0"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-78", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [835.0, 383.0, 32.0, 18.0], "style": "", "text": "path"}}, {"box": {"comment": "ID", "id": "obj-81", "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [816.0, 383.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"color": [0.984314, 0.819608, 0.05098, 1.0], "fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-76", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [816.0, 351.0, 59.0, 20.0], "saved_object_attributes": {"_persistence": 0}, "style": "", "text": "live.object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-72", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [795.0, 383.0, 20.0, 18.0], "style": "", "text": "ID"}}, {"box": {"comment": "ID", "id": "obj-73", "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [776.0, 383.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [776.0, 329.0, 99.0, 20.0], "style": "", "text": "t l getpath l"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-179", "linecount": 2, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [456.0, 453.0, 96.0, 29.0], "style": "", "text": "Some functions may return values"}}, {"box": {"fontname": "Arial Bold Italic", "fontsize": 18.0, "id": "obj-176", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [536.0, 24.0, 123.0, 27.0], "style": "", "text": "Live.function", "textcolor": [0.301961, 0.337255, 0.403922, 1.0]}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-130", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [584.0, 136.0, 32.0, 18.0], "style": "", "text": "Path"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-128", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [512.0, 432.0, 43.0, 18.0], "style": "", "text": "Return"}}, {"box": {"comment": "Return", "id": "obj-129", "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [488.0, 432.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-125", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [640.0, 287.0, 50.0, 20.0], "style": "", "text": "prepend"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-124", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [424.0, 160.0, 67.0, 20.0], "style": "", "text": "prepend set"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-123", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [352.0, 304.0, 69.0, 20.0], "style": "", "text": "prepend call"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-54", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [371.0, 136.0, 35.0, 18.0], "style": "", "text": "<PERSON>"}}, {"box": {"blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "id": "obj-53", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [352.0, 136.0, 18.0, 18.0], "style": ""}}, {"box": {"blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "id": "obj-122", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [280.0, 167.0, 18.0, 18.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-121", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [280.0, 136.0, 60.0, 20.0], "style": "", "text": "route start"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-120", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [352.0, 184.0, 37.0, 20.0], "style": "", "text": "zl reg"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-112", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [96.0, 112.0, 79.0, 18.0], "style": "", "text": "Arg: function"}}, {"box": {"fontname": "Arial Bold Italic", "fontsize": 12.0, "id": "obj-109", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [72.0, 197.0, 68.0, 20.0], "style": "", "text": "Messages", "textcolor": [0.301961, 0.337255, 0.403922, 1.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-107", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 416.0, 117.0, 18.0], "style": "", "text": "Optional function values"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-108", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 400.0, 54.0, 18.0], "style": "", "text": "anything"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-105", "linecount": 2, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 368.0, 162.0, 29.0], "style": "", "text": "<start>, data..., <done>. Required for some MIDI note operations"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-106", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 352.0, 57.0, 18.0], "style": "", "text": "sequence"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-102", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 320.0, 32.0, 18.0], "style": "", "text": "path"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-99", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 304.0, 106.0, 18.0], "style": "", "text": "The id of the object"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-100", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 288.0, 19.0, 18.0], "style": "", "text": "id"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-95", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 224.0, 35.0, 18.0], "style": "", "text": "bang"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-93", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [656.0, 136.0, 97.0, 18.0], "style": "", "text": "< Anything (value)"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-85", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [560.0, 355.0, 44.0, 20.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-84", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [216.0, 416.0, 155.0, 20.0], "style": "", "text": "fire id 0"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-82", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [640.0, 363.0, 32.0, 18.0], "style": "", "text": "Path"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-80", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [608.0, 355.0, 20.0, 18.0], "style": "", "text": "ID"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-79", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [475.0, 136.0, 20.0, 18.0], "style": "", "text": "ID"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-77", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [560.0, 379.0, 160.0, 20.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-74", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [496.0, 136.0, 40.0, 20.0], "prototypename": "Live", "style": "", "triscale": 0.75}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-66", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [216.0, 360.0, 155.0, 20.0], "style": "", "text": "call fire"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-61", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 272.0, 157.0, 18.0], "style": "", "text": "The function of the object to fire"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-60", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 256.0, 50.0, 18.0], "style": "", "text": "function"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-55", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 240.0, 127.0, 18.0], "style": "", "text": "Fire the selected function"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-18", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [176.0, 136.0, 35.0, 18.0], "style": "", "text": "Done"}}, {"box": {"blinkcolor": [0.921569, 0.94902, 0.05098, 1.0], "id": "obj-47", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [208.0, 136.0, 18.0, 18.0], "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 7, "numoutlets": 7, "outlettype": ["", "", "", "", "", "", ""], "patching_rect": [208.0, 112.0, 451.0, 20.0], "style": "", "text": "route done sequence bang function id path"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [496.0, 248.0, 62.0, 20.0], "style": "", "text": "prepend id"}}, {"box": {"comment": "Path or ID", "id": "obj-33", "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [304.0, 24.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [96.0, 95.0, 92.0, 20.0], "style": "", "text": "prepend function"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-49", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [568.0, 248.0, 74.0, 20.0], "style": "", "text": "prepend path"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-50", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [72.0, 24.0, 124.0, 18.0], "style": "", "text": "Attributes (= messages)"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-56", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [96.0, 40.0, 68.0, 20.0], "style": "", "text": "patcherargs"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-65", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [227.0, 24.0, 76.0, 18.0], "style": "", "text": "< Messages >"}}, {"box": {"color": [0.984314, 0.819608, 0.05098, 1.0], "fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-69", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", ""], "patching_rect": [568.0, 312.0, 53.0, 20.0], "style": "", "text": "live.path"}}, {"box": {"fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-94", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [456.0, 392.0, 51.0, 20.0], "style": "", "text": "zl slice 1"}}, {"box": {"color": [0.984314, 0.819608, 0.05098, 1.0], "fontname": "Arial Bold", "fontsize": 10.0, "id": "obj-96", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [456.0, 360.0, 59.0, 20.0], "saved_object_attributes": {"_persistence": 0}, "style": "", "text": "live.object"}}, {"box": {"comment": "property <symbol>, observe <1/0>, get, bang (get), set <property>, anything (property data)", "id": "obj-119", "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [208.0, 24.0, 18.0, 18.0], "prototypename": "M4L.Arial10", "style": ""}}, {"box": {"angle": 0.0, "bgcolor": [0.094118, 0.113725, 0.137255, 0.0], "border": 1, "id": "obj-46", "maxclass": "panel", "mode": 0, "numinlets": 1, "numoutlets": 0, "patching_rect": [24.0, 216.0, 176.0, 229.0], "proportion": 0.39, "rounded": 16, "style": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-3", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [32.0, 336.0, 122.0, 18.0], "style": "", "text": "The path of the object"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-8", "linecount": 3, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [328.0, 24.0, 193.0, 40.0], "style": "", "text": "< For clarity and convenience, you may want to use one inlet to set messages, and another inlet to set values."}}], "lines": [{"patchline": {"destination": ["obj-49", 0], "disabled": 0, "hidden": 0, "source": ["obj-1", 1]}}, {"patchline": {"destination": ["obj-5", 0], "disabled": 0, "hidden": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-43", 0], "disabled": 0, "hidden": 0, "source": ["obj-119", 0]}}, {"patchline": {"destination": ["obj-123", 0], "disabled": 0, "hidden": 0, "source": ["obj-120", 0]}}, {"patchline": {"destination": ["obj-122", 0], "disabled": 0, "hidden": 0, "source": ["obj-121", 0]}}, {"patchline": {"destination": ["obj-123", 0], "disabled": 0, "hidden": 0, "source": ["obj-121", 1]}}, {"patchline": {"destination": ["obj-120", 0], "disabled": 0, "hidden": 0, "source": ["obj-122", 0]}}, {"patchline": {"destination": ["obj-66", 1], "disabled": 0, "hidden": 0, "source": ["obj-123", 0]}}, {"patchline": {"destination": ["obj-96", 0], "disabled": 0, "hidden": 0, "source": ["obj-123", 0]}}, {"patchline": {"destination": ["obj-125", 0], "disabled": 0, "hidden": 0, "source": ["obj-124", 0]}}, {"patchline": {"destination": ["obj-123", 0], "disabled": 0, "hidden": 0, "source": ["obj-125", 0]}}, {"patchline": {"destination": ["obj-96", 1], "disabled": 0, "hidden": 0, "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-43", 0], "disabled": 0, "hidden": 0, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-43", 0], "disabled": 0, "hidden": 0, "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-23", 0], "disabled": 0, "hidden": 0, "source": ["obj-4", 1]}}, {"patchline": {"destination": ["obj-5", 0], "disabled": 0, "hidden": 0, "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-73", 0], "disabled": 0, "hidden": 0, "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-76", 1], "disabled": 0, "hidden": 0, "source": ["obj-41", 2]}}, {"patchline": {"destination": ["obj-76", 0], "disabled": 0, "hidden": 0, "source": ["obj-41", 1]}}, {"patchline": {"destination": ["obj-1", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 5]}}, {"patchline": {"destination": ["obj-120", 1], "disabled": 0, "hidden": 0, "source": ["obj-43", 3]}}, {"patchline": {"destination": ["obj-121", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 1]}}, {"patchline": {"destination": ["obj-124", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 3]}}, {"patchline": {"destination": ["obj-125", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 6]}}, {"patchline": {"destination": ["obj-47", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-53", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 2]}}, {"patchline": {"destination": ["obj-74", 0], "disabled": 0, "hidden": 0, "source": ["obj-43", 4]}}, {"patchline": {"destination": ["obj-69", 0], "disabled": 0, "hidden": 0, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-77", 1], "disabled": 0, "hidden": 0, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-41", 0], "disabled": 0, "hidden": 0, "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-120", 0], "disabled": 0, "hidden": 0, "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-34", 0], "disabled": 0, "hidden": 0, "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-43", 0], "disabled": 0, "hidden": 0, "source": ["obj-56", 1]}}, {"patchline": {"destination": ["obj-41", 0], "disabled": 0, "hidden": 0, "source": ["obj-69", 1]}}, {"patchline": {"destination": ["obj-85", 1], "disabled": 0, "hidden": 0, "source": ["obj-69", 1]}}, {"patchline": {"destination": ["obj-96", 1], "disabled": 0, "hidden": 0, "source": ["obj-69", 1]}}, {"patchline": {"destination": ["obj-4", 0], "disabled": 0, "hidden": 0, "source": ["obj-74", 0]}}, {"patchline": {"destination": ["obj-81", 0], "disabled": 0, "hidden": 0, "source": ["obj-76", 0]}}, {"patchline": {"destination": ["obj-129", 0], "disabled": 0, "hidden": 0, "source": ["obj-94", 1]}}, {"patchline": {"destination": ["obj-84", 1], "disabled": 0, "hidden": 0, "source": ["obj-96", 0]}}, {"patchline": {"destination": ["obj-94", 0], "disabled": 0, "hidden": 0, "source": ["obj-96", 0]}}]}}