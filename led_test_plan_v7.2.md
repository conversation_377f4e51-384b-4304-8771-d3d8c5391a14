# LED Control v7.2 - Dedicated Button_Matrix Object - June 27, 2025

## 🎯 **CRITICAL FIX: Dedicated Button_Matrix live.object**

### ✅ **Root Cause Identified:**
The forum search revealed that `send_value` commands must go to a `live.object` specifically connected to **Button_Matrix control #20**.

### ✅ **New Implementation v7.2:**

**Max Patch (clipQueue.maxpat):**
- ✅ **Added dedicated live.object** (obj-209f) for Button_Matrix LED control
- ✅ **Added path setup** (obj-209g): `"path control_surfaces 0 controls 20"`
- ✅ **Added loadbang** (obj-209h) to auto-configure Button_Matrix path on startup
- ✅ **Updated routing:** LED commands → dedicated Button_Matrix live.object

**JavaScript (clipLauncher.js v7.2):**
- ✅ **Same send_value commands:** `outlet(0, "call", "send_value", scene, track, velocity)`
- ✅ **Correct coordinate format:** X=column, Y=row, Z=color (0-127)

### 🧪 **Expected Test Results:**

**When patch loads:**
```
path control_surfaces 0 controls 20    (auto-setup Button_Matrix)
```

**When holding Session button:**
```
LED_DEBUG: session_enter
MIDI_OUT: send_value 0 0 127           (column 0, row 0, color 127)
MIDI_OUT: send_value 1 0 127           (column 1, row 0, color 127)
MIDI_OUT: send_value 0 1 126           (column 0, row 1, color 126)
MIDI_OUT: send_value 1 1 126           (column 1, row 1, color 126)
...continuous updates...
```

**Physical Result:** Bottom 2×2 pads should light up and STAY lit

**NO MORE:** `live.object: doesn't understand "send_value"` errors

### 🔧 **Technical Details:**

**Push 2 Button_Matrix API:**
- **Control ID:** 20 (standard Push 2 Button_Matrix)
- **Path:** `control_surfaces 0 controls 20`
- **Command:** `call send_value X Y Z`
- **Coordinates:** X=column (0-7), Y=row (0-7), Z=velocity (0-127)

**Key Insight from Forum:**
> "You can control the LEDs by sending a 'call send_value X Y Z' message, where X is the column, Y is the row, and Z is the color value from 0-127."

### 📊 **Architecture:**

```
JavaScript → route → dedicated Button_Matrix live.object → Push 2 LEDs
```

**vs. Previous (broken):**
```
JavaScript → route → generic live.object → ERROR
```

### 🚀 **Why This Should Work:**

1. **Dedicated object:** live.object specifically for Button_Matrix control #20
2. **Auto-configuration:** loadbang sets correct path on startup
3. **Forum-verified method:** Exact approach confirmed by Cycling '74 community
4. **Proper API calls:** send_value with correct X/Y/Z format

**Status: Ready for Button_Matrix LED API testing!** 🎛️🔥
