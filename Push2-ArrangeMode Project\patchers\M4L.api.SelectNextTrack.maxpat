{"patcher": {"fileversion": 1, "rect": [101.0, 54.0, 517.0, 426.0], "bglocked": 0, "defrect": [101.0, 54.0, 517.0, 426.0], "openrect": [0.0, 0.0, 0.0, 0.0], "openinpresentation": 0, "default_fontsize": 10.0, "default_fontface": 0, "default_fontname": "Arial Bold", "gridonopen": 0, "gridsize": [8.0, 8.0], "gridsnaponopen": 0, "toolbarvisible": 1, "boxanimatetime": 200, "imprint": 0, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "boxes": [{"box": {"maxclass": "comment", "text": "< selects the track with the given index - only visible tracks and NOT return tracks are respected", "linecount": 4, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "patching_rect": [352.0, 360.0, 140.0, 52.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-6"}}, {"box": {"maxclass": "comment", "text": "< if the currently selected track is the last track in our track list we want to jump to the Master Track", "linecount": 3, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "patching_rect": [296.0, 208.0, 195.0, 41.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-11"}}, {"box": {"maxclass": "comment", "text": "< Get the number of visible tracks (NOT including the return tracks)", "linecount": 2, "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "patching_rect": [288.0, 136.0, 192.0, 29.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-9"}}, {"box": {"maxclass": "comment", "text": "select the master track", "fontname": "<PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "fontsize": 10.0, "patching_rect": [32.0, 320.0, 142.0, 18.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-7"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t i b", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 2, "fontsize": 10.0, "outlettype": ["int", "bang"], "patching_rect": [184.0, 272.0, 32.5, 18.0], "id": "obj-5"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "t i i i", "fontname": "Arial Bold", "numinlets": 1, "numoutlets": 3, "fontsize": 10.0, "outlettype": ["int", "int", "int"], "patching_rect": [120.0, 184.0, 147.0, 18.0], "id": "obj-4"}}, {"box": {"maxclass": "button", "prototypename": "M4L.patching", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [120.0, 112.0, 18.0, 18.0], "id": "obj-12"}}, {"box": {"maxclass": "comment", "prototypename": "ML.subpatcher-title", "text": "Select Next Track", "fontname": "Arial Bold Italic", "numinlets": 1, "numoutlets": 0, "fontsize": 24.0, "patching_rect": [16.0, 16.0, 259.0, 34.0], "textcolor": [0.3, 0.34, 0.4, 1.0], "frgb": [0.3, 0.34, 0.4, 1.0], "id": "obj-48"}}, {"box": {"maxclass": "comment", "prototypename": "<PERSON><PERSON><PERSON>patcher-story", "text": "<PERSON> selects the next track in the current Live Set.", "fontname": "Arial Italic", "numinlets": 1, "numoutlets": 0, "fontsize": 11.0, "patching_rect": [16.0, 48.0, 277.0, 19.0], "frgb": [0.0, 0.0, 0.0, 1.0], "id": "obj-50"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "sel", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 2, "fontsize": 10.0, "outlettype": ["bang", ""], "patching_rect": [120.0, 240.0, 83.0, 18.0], "id": "obj-2"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "M4L.api.SelectMasterTrack", "fontname": "Arial Bold", "numinlets": 1, "color": [0.545098, 0.85098, 0.592157, 1.0], "numoutlets": 0, "fontsize": 10.0, "patching_rect": [32.0, 304.0, 140.0, 18.0], "id": "obj-1"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "M4L.api.GetSelectedTrackIndex", "fontname": "Arial Bold", "numinlets": 1, "color": [0.545098, 0.85098, 0.592157, 1.0], "numoutlets": 2, "fontsize": 10.0, "outlettype": ["int", "bang"], "patching_rect": [120.0, 208.0, 162.0, 18.0], "id": "obj-3"}}, {"box": {"maxclass": "message", "text": "0", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [224.0, 304.0, 31.0, 16.0], "id": "obj-14"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "- 1", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": ["int"], "patching_rect": [120.0, 160.0, 32.0, 18.0], "id": "obj-15"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "clip", "fontname": "Arial Bold", "numinlets": 3, "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [184.0, 336.0, 83.0, 18.0], "id": "obj-17"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "+ 1", "fontname": "Arial Bold", "numinlets": 2, "numoutlets": 1, "fontsize": 10.0, "outlettype": ["int"], "patching_rect": [184.0, 304.0, 32.0, 18.0], "id": "obj-18"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "M4L.api.SetSelectedTrackIndex", "fontname": "Arial Bold", "numinlets": 1, "color": [0.545098, 0.85098, 0.592157, 1.0], "numoutlets": 0, "fontsize": 10.0, "patching_rect": [184.0, 360.0, 161.0, 18.0], "id": "obj-19"}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "M4L.api.GetVisibleTrackCount", "fontname": "Arial Bold", "numinlets": 1, "color": [0.545098, 0.85098, 0.592157, 1.0], "numoutlets": 1, "fontsize": 10.0, "outlettype": [""], "patching_rect": [120.0, 136.0, 155.0, 18.0], "id": "obj-20"}}, {"box": {"maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [120.0, 80.0, 18.0, 18.0], "id": "obj-25", "comment": ""}}], "lines": [{"patchline": {"source": ["obj-12", 0], "destination": ["obj-20", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-20", 0], "destination": ["obj-15", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-18", 0], "destination": ["obj-17", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-3", 0], "destination": ["obj-2", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-17", 0], "destination": ["obj-19", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-25", 0], "destination": ["obj-12", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 2], "destination": ["obj-17", 2], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 1], "destination": ["obj-2", 1], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-4", 0], "destination": ["obj-3", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-15", 0], "destination": ["obj-4", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-2", 0], "destination": ["obj-1", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-5", 1], "destination": ["obj-14", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-5", 0], "destination": ["obj-18", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-2", 1], "destination": ["obj-5", 0], "hidden": 0, "midpoints": []}}, {"patchline": {"source": ["obj-14", 0], "destination": ["obj-17", 1], "hidden": 0, "midpoints": []}}]}}