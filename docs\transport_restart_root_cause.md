## Transport Restart Root Cause Analysis

Based on the debug logs, I've identified the **root cause** of the transport restart issue:

### The Problem
Our Session + Play detection and JavaScript execution work **perfectly**. The issue is not in our code, but in **Play button blocking**.

### Evidence from Debug Logs
```
session_button_send: 1     <- Session pressed  
gate_setting: 2            <- Gate switches to Session mode
...our JavaScript executes perfectly calling launch_all_queued()...
session_button_send: 0     <- Session released
gate_setting: 1            <- <PERSON> switches back to normal mode  
<-- TRANSPORT RESTART HAPPENS HERE -->
```

### Root Cause
When Session + Play is pressed:
1. Session button activates our custom logic ✅ 
2. Our JavaScript executes perfectly ✅
3. Session button is released, gate switches back to normal mode
4. **Play button value (which was held during combo) reaches Live and causes transport restart** ❌

### The Solution
We need to **consume/block the Play button entirely** when Session mode is active, preventing it from ever reaching Ableton Live.

### Current Gate Logic Issue
The current gate system routes our **command** differently, but doesn't actually **block the Play button from reaching Live**. When Session is released, the held Play button value gets processed by Live as a normal play command.

### Fix Required
Add logic to actively consume/block Play button values when Session mode is active or was recently active, preventing them from reaching Live's transport controls.

### Test Plan
1. Monitor raw Play button values during Session + Play
2. Verify Play button blocking is working
3. Add delay or consumption logic to prevent Play button from reaching Live after Session release
