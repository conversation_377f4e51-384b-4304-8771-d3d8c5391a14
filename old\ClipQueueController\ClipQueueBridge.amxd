// Max for Live device to route MIDI from Push 2 to ClipQueueController
// and provide visual feedback

// Create a midiin object to receive MIDI from Push 2
// This would be configured to the same MIDI port as Push 2
[midiin]

// Create a midiout object to send to a virtual MIDI port
// that <PERSON>lip<PERSON>ueueController will listen to
[midiout]

// Create UI elements to show queued clips
[live.grid]

// Logic to detect Session button + pad combinations
// and route appropriate MIDI messages