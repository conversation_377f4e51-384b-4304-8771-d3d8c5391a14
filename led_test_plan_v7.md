# LED Control Test Plan v7.0 - June 27, 2025

## 🎯 NEW FEATURES: Continuous LED Updates + MIDI Device Targeting

### ✅ **Latest Implementation:**

**JavaScript (clipLauncher.js v7.0):**
- ✅ Continuous LED updates every 100ms to maintain control
- ✅ Dedicated `send_test_led_commands()` function  
- ✅ Task-based LED refresh system to override Push 2's normal behavior
- ✅ Proper task cleanup on session exit

**Max Patch (clipQueue.maxpat):**
- ✅ Updated `noteout 1 "Ableton Push 2"` with device targeting
- ✅ Added `print MIDI_OUT` for debugging MIDI commands
- ✅ Complete signal path: JavaScript → route → unpack → noteout → Push 2

### 🧪 **Expected Test Results:**

**When holding Session button:**
```
LED_DEBUG: session_enter
LED_DEBUG: clip_found 0 0 36
LED_DEBUG: clip_found 0 1 37
...
MIDI_OUT: 36 126 1    (pad 36, green, channel 1)
MIDI_OUT: 37 127 1    (pad 37, red if playing, channel 1)
MIDI_OUT: 44 126 1    (pad 44, green, channel 1)
MIDI_OUT: 45 126 1    (pad 45, green, channel 1)
...continuous updates every 100ms...
```

**Physical Result:** Pads 36, 37, 44, 45 should light up and STAY lit while Session held

**When releasing Session button:**
```
LED_DEBUG: session_exit
MIDI_OUT: 36 0 1      (turn off pad 36)
MIDI_OUT: 37 0 1      (turn off pad 37) 
MIDI_OUT: 44 0 1      (turn off pad 44)
MIDI_OUT: 45 0 1      (turn off pad 45)
```

**Physical Result:** Test area LEDs should turn off

### 🔧 **Troubleshooting Guide:**

**If LEDs still don't stay lit:**

1. **Check MIDI Device Name:**
   - Max Console may show device not found
   - Try changing `"Ableton Push 2"` to just `"Push 2"` or check MIDI setup

2. **Check MIDI_OUT Messages:**
   - Should see `MIDI_OUT: 36 126 1` style messages
   - If no MIDI_OUT messages → JavaScript routing issue
   - If MIDI_OUT messages but no LEDs → MIDI device issue

3. **Try Different Colors:**
   - Change 126/127 to 21 (bright green) or 5 (red)
   - Some Push 2 units respond better to specific velocity values

4. **MIDI Channel:**
   - Try channel 0 instead of 1: `outlet(0, "led_control", pad_note, led_color, 0)`

### 🎛️ **Push 2 LED Color Reference:**

- **0:** Off
- **1-4:** Red shades  
- **5:** Bright red
- **17-20:** Orange shades
- **21:** Bright green
- **126:** Standard green
- **127:** Bright red/white

### 📊 **Success Criteria:**

1. ✅ **Max Console shows:** LED_DEBUG and MIDI_OUT messages
2. 🔍 **Push 2 Hardware:** Bottom-left 2×2 pads light up during Session mode
3. ✅ **Persistence:** LEDs stay lit while Session button held
4. ✅ **Cleanup:** LEDs turn off when Session button released

### 🚀 **Next Steps If Successful:**

1. Expand to full 8×8 matrix
2. Add real-time clip state monitoring
3. Implement proper color coding
4. Optimize update frequency
5. Add clip state change detection

**Current Status: Ready for continuous LED testing with Push 2 device targeting!**
