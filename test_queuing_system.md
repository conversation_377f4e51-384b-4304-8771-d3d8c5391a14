# Testing the New Ableton Internal Queuing System

## Overview
The clipQueue.maxpat has been updated to use Ableton's internal queuing system via `set_fire_button_state` instead of immediate clip firing. This should provide perfectly synchronized clip launching.

## Changes Made
1. **Clip Queuing**: Changed from `call fire` to `call set_fire_button_state 1`
2. **Global Launch**: Added "Launch All Queued Clips" button that sends `call set_fire_button_state 0` to all queued clips
3. **Queue Storage**: Added `coll queued_clips` to track which clips are in the queued state
4. **Clear Queue**: Added "Clear All Queued Clips" button to reset all fire button states

## Testing Steps

### 1. Basic Queuing Test
1. Open the clipQueue.maxpat in Max
2. Initialize Push2 connection
3. Press Session button + pads to queue clips
4. **Expected**: Clips should enter "pulsing" state (blinking) in Ableton but not launch
5. **Expected**: No audio should play yet

### 2. Global Launch Test
1. With clips queued (pulsing state)
2. Click "Launch All Queued Clips" button in the Max patch
3. **Expected**: All queued clips should launch simultaneously and immediately
4. **Expected**: Perfect synchronization with no perceptible delay between clips

### 3. Clear Queue Test
1. Queue some clips (pulsing state)
2. Click "Clear All Queued Clips" button
3. **Expected**: All clips should stop pulsing and return to normal state
4. **Expected**: No clips should launch

### 4. Mixed Timing Test
1. Queue clips with different lengths and tempos
2. Launch all at once
3. **Expected**: All clips start exactly at the same time regardless of their individual properties

## Technical Implementation

### Key Benefits
- **Native Timing**: Uses Ableton's internal timing system, not Max's timing
- **Perfect Sync**: All clips use the same internal launch mechanism
- **Visual Feedback**: Clips show proper "queued" state (pulsing)
- **Immediate Response**: Launch happens on Ableton's high-priority audio thread

### How It Works
1. `set_fire_button_state 1` puts clips in Ableton's internal queue
2. Clips enter "pulsing" state (same as when Stop is pressed while clips are playing)
3. `set_fire_button_state 0` releases all clips simultaneously
4. Ableton's native timing ensures perfect synchronization

## Expected Results
- Zero perceptible launch delay compared to simultaneously triggered clips
- Perfect synchronization even with "Global Quantization: None"
- Clips maintain their individual timing/quantization settings
- Visual feedback matches Ableton's native behavior
