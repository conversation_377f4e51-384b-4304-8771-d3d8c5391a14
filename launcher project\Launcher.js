inlets = 1;
outlets = 2;
var track = 0;
var clip_slot = 0;
var midi_val = 0;
var enabled = new Array();
function bang() {
  var api = new LiveAPI("live_set tracks");
	var child = api.getcount();
  log('this device path:', child);
  // do stuff with the Device object...
}

function log() {
  for(var i=0,len=arguments.length; i<len; i++) {
    var message = arguments[i];
    if(message && message.toString) {
      var s = message.toString();
      if(s.indexOf("[object ") >= 0) {
        s = JSON.stringify(message);
      }
      post(s);
    }
    else if(message === null) {
      post("<null>");
    }
    else {
      post(message);
    }
  }
  post("\n");
}
function set_params(n, n2, n3){
	log(n, n2, n3);
	track = n;
	clip_slot = n2;
	midi_val = n3;
}
function output(a, b){
	outlet(1,a,b);
	}

function msg_int(x){
	log("in msg_int");
	log("x is " + x);
	var padNum = x%midi_val;
	log(track, x, midi_val, clip_slot);

	if(enabled[padNum] == 2){
		var trackSel = track + padNum;
		var liveObject = new LiveAPI("live_set tracks " + trackSel + " clip_slots " + clip_slot);
	log(liveObject.get("is_playing"));
		args = new Array(2);
			args[0] = x; 
if(liveObject.get("is_playing") == 1){
				log("stopped");
				args[1] = 0;
			}
		else{
				log("playing");
				args[1] = 127
			}		
			tsk = new Task(output, this, args);
			tsk.schedule(500);
liveObject.call("fire");
		
			
		
	} else{
		log("pad disabled");
	}
	
}
function list(){
	log("list");
	enabled = arguments;
	for(x = 0; x < arguments.length; x++){
	log(arguments[x]);
	}
}