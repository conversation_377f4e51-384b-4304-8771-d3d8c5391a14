{"boxes": [{"box": {"maxclass": "message", "text": "200", "patching_rect": [659.0, 334.0, 29.5, 20.0], "numinlets": 2, "id": "obj-33", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "del", "patching_rect": [595.0, 338.0, 29.5, 20.0], "numinlets": 2, "id": "obj-31", "numoutlets": 1, "outlettype": ["bang"]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "bangbang", "patching_rect": [578.0, 285.0, 58.0, 20.0], "numinlets": 1, "id": "obj-30", "numoutlets": 2, "outlettype": ["bang", "bang"]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "if $i1 > 0 then $i2", "patching_rect": [446.0, 321.0, 91.0, 20.0], "numinlets": 2, "id": "obj-27", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "message", "text": "0", "patching_rect": [436.0, 381.0, 50.0, 20.0], "numinlets": 2, "id": "obj-16", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "textbutton", "presentation_rect": [5.0, 117.0, 165.0, 20.0], "patching_rect": [158.0, 464.0, 223.0, 28.0], "numinlets": 1, "text": "Configure Lowest Note", "bgoncolor": [0.784314, 0.145098, 0.023529, 1.0], "usebgoncolor": 1, "id": "obj-8", "bgcolor": [0.311041, 0.314599, 0.318357, 1.0], "mode": 1, "numoutlets": 3, "texton": "Configure Lowest Note On", "parameter_enable": 0, "outlettype": ["", "", "int"], "textoncolor": [1.0, 1.0, 1.0, 1.0], "presentation": 1, "saved_attribute_attributes": {"bgcolor": {"expression": ""}, "bgoncolor": {"expression": ""}, "textoncolor": {"expression": ""}}}}, {"box": {"maxclass": "textbutton", "presentation_rect": [5.0, 3.0, 165.0, 34.0], "patching_rect": [14.0, 313.0, 161.0, 13.0], "numinlets": 1, "text": "Configure Triggers", "id": "obj-26", "numoutlets": 3, "parameter_enable": 0, "outlettype": ["", "", "int"], "presentation": 1}}, {"box": {"maxclass": "comment", "text": "Number of Midi Notes", "presentation_rect": [58.0, 42.0, 112.0, 18.0], "patching_rect": [123.5, 375.5, 150.0, 18.0], "numinlets": 1, "id": "obj-2", "numoutlets": 0, "presentation": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pcontrol", "patching_rect": [14.0, 373.0, 50.0, 20.0], "numinlets": 1, "id": "obj-24", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "message", "text": "open", "patching_rect": [14.0, 339.0, 34.0, 20.0], "numinlets": 2, "id": "obj-18", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "live.numbox", "varname": "live.numbox[3]", "presentation_rect": [5.0, 44.0, 45.0, 15.0], "patching_rect": [85.0, 375.5, 36.0, 15.0], "numinlets": 1, "id": "obj-12", "numoutlets": 2, "parameter_enable": 1, "outlettype": ["", "float"], "presentation": 1, "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.numbox[3]", "parameter_shortname": "live.numbox[3]", "parameter_type": 0, "parameter_unitstyle": 0}}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "p config", "patching_rect": [14.0, 410.0, 49.0, 20.0], "numinlets": 1, "id": "obj-11", "numoutlets": 1, "outlettype": [""], "patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "openrect": [411.0, 291.0, 472.0, 215.0], "bglocked": 0, "openinpresentation": 1, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 472.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"maxclass": "comment", "text": "Hell No, Ain't\nLaunchin for \nyour ass", "linecount": 3, "presentation_linecount": 3, "presentation_rect": [9.0, 138.0, 87.0, 48.0], "patching_rect": [7.0, 110.0, 150.0, 48.0], "numinlets": 1, "id": "obj-10", "numoutlets": 0, "presentation": 1}}, {"box": {"maxclass": "comment", "text": "Launch dat\n<PERSON><PERSON>", "linecount": 2, "presentation_linecount": 2, "presentation_rect": [9.0, 46.0, 87.0, 34.0], "patching_rect": [264.0, 312.0, 150.0, 34.0], "numinlets": 1, "id": "obj-6", "numoutlets": 0, "presentation": 1}}, {"box": {"maxclass": "outlet", "patching_rect": [62.0, 336.5, 30.0, 30.0], "numinlets": 1, "id": "obj-4", "numoutlets": 0, "comment": "", "index": 1}}, {"box": {"maxclass": "inlet", "patching_rect": [145.0, 340.5, 30.0, 30.0], "numinlets": 0, "id": "obj-2", "numoutlets": 1, "outlettype": [""], "comment": "", "index": 1}}, {"box": {"maxclass": "message", "text": "columns $1", "patching_rect": [145.0, 387.849609, 70.0, 22.0], "numinlets": 2, "id": "obj-12", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "live.grid", "varname": "live.grid", "presentation_rect": [109.5, 4.0, 359.0, 206.0], "patching_rect": [1.0, 1.849609, 465.0, 211.0], "columns": 1, "rows": 2, "numinlets": 2, "direction": 0, "id": "obj-126", "numoutlets": 6, "parameter_enable": 1, "outlettype": ["", "", "", "", "", ""], "presentation": 1, "saved_attribute_attributes": {"valueof": {"parameter_initial": [2, 8, 2, 0, 8, 1, 1001, 2001, 3001, 4001, 5001, 6001, 7001, 2, 2, 2, 2, 2, 2, 2, 2], "parameter_invisible": 1, "parameter_longname": "live.grid", "parameter_shortname": "live.grid", "parameter_type": 3}}}}], "lines": [{"patchline": {"source": ["obj-2", 0], "destination": ["obj-12", 0]}}, {"patchline": {"source": ["obj-126", 1], "destination": ["obj-4", 0]}}, {"patchline": {"source": ["obj-12", 0], "destination": ["obj-126", 0]}}]}, "saved_object_attributes": {"description": "", "digest": "", "editing_bgcolor": [0.56078431372549, 0.56078431372549, 0.56078431372549, 1.0], "globalpatchername": "", "locked_bgcolor": [0.56078431372549, 0.56078431372549, 0.56078431372549, 1.0], "tags": ""}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pak 0 0 0", "patching_rect": [14.0, 133.0, 53.0, 20.0], "numinlets": 3, "id": "obj-1", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "message", "text": "200", "patching_rect": [220.25, 128.0, 29.5, 20.0], "numinlets": 2, "id": "obj-25", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "del", "patching_rect": [179.0, 128.0, 29.5, 20.0], "numinlets": 2, "id": "obj-20", "numoutlets": 1, "outlettype": ["bang"]}}, {"box": {"maxclass": "message", "text": "outputvalue", "patching_rect": [188.0, 60.0, 67.0, 20.0], "numinlets": 2, "id": "obj-19", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "bangbang", "patching_rect": [179.0, 97.0, 58.0, 20.0], "numinlets": 1, "id": "obj-17", "numoutlets": 2, "outlettype": ["bang", "bang"]}}, {"box": {"maxclass": "comment", "text": "Lowest Note Value", "linecount": 3, "presentation_rect": [58.0, 93.0, 116.0, 18.0], "patching_rect": [125.0, 20.5, 48.0, 41.0], "numinlets": 1, "id": "obj-23", "numoutlets": 0, "presentation": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "round", "patching_rect": [125.0, 97.0, 39.0, 20.0], "numinlets": 2, "id": "obj-22", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "live.numbox", "varname": "live.numbox[2]", "presentation_rect": [5.0, 95.0, 45.0, 15.0], "patching_rect": [125.0, 69.0, 36.0, 15.0], "numinlets": 1, "id": "obj-21", "numoutlets": 2, "parameter_enable": 1, "outlettype": ["", "float"], "presentation": 1, "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.numbox[2]", "parameter_shortname": "live.numbox[2]", "parameter_type": 0, "parameter_unitstyle": 0}}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "round", "patching_rect": [69.5, 97.0, 39.0, 20.0], "numinlets": 2, "id": "obj-15", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "round", "patching_rect": [14.0, 97.0, 39.0, 20.0], "numinlets": 2, "id": "obj-13", "numoutlets": 1, "outlettype": [""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "midiformat", "patching_rect": [380.0, 183.5, 82.0, 20.0], "numinlets": 7, "id": "obj-10", "numoutlets": 2, "outlettype": ["int", ""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "unpack", "patching_rect": [380.0, 84.5, 46.0, 20.0], "numinlets": 1, "id": "obj-7", "numoutlets": 2, "outlettype": ["int", "int"]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "midiout", "patching_rect": [380.0, 215.0, 47.0, 20.0], "numinlets": 1, "id": "obj-5", "numoutlets": 0}}, {"box": {"maxclass": "comment", "text": "Pad 1 Track", "linecount": 2, "presentation_rect": [58.0, 59.0, 112.0, 18.0], "patching_rect": [14.0, 22.0, 39.5, 29.0], "numinlets": 1, "id": "obj-6", "numoutlets": 0, "presentation": 1}}, {"box": {"maxclass": "comment", "text": "<PERSON><PERSON>", "linecount": 2, "presentation_rect": [58.0, 76.0, 112.0, 18.0], "patching_rect": [68.5, 20.5, 47.0, 29.0], "numinlets": 1, "id": "obj-4", "numoutlets": 0, "presentation": 1}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "b", "patching_rect": [14.0, 180.0, 29.5, 20.0], "numinlets": 1, "id": "obj-139", "numoutlets": 2, "hidden": 1, "outlettype": ["bang", "bang"]}}, {"box": {"maxclass": "live.numbox", "varname": "live.numbox[1]", "presentation_rect": [5.0, 61.0, 45.0, 15.0], "patching_rect": [14.0, 69.0, 36.0, 15.0], "numinlets": 1, "id": "obj-135", "numoutlets": 2, "parameter_enable": 1, "outlettype": ["", "float"], "presentation": 1, "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.numbox[1]", "parameter_shortname": "live.numbox", "parameter_type": 0, "parameter_unitstyle": 0}}}}, {"box": {"maxclass": "live.numbox", "varname": "live.numbox", "presentation_rect": [5.0, 78.0, 45.0, 15.0], "patching_rect": [69.5, 69.0, 36.0, 15.0], "numinlets": 1, "id": "obj-134", "numoutlets": 2, "parameter_enable": 1, "outlettype": ["", "float"], "presentation": 1, "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.numbox", "parameter_shortname": "live.numbox", "parameter_type": 0, "parameter_unitstyle": 0}}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "pack 0 0 0", "patching_rect": [82.0, 133.0, 58.0, 20.0], "numinlets": 3, "id": "obj-122", "numoutlets": 1, "hidden": 1, "outlettype": [""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "loadbang", "patching_rect": [188.0, 30.5, 55.0, 20.0], "numinlets": 1, "id": "obj-117", "numoutlets": 1, "hidden": 1, "outlettype": ["bang"]}}, {"box": {"maxclass": "message", "text": "set_params $1 $2 $3", "patching_rect": [14.0, 215.0, 108.0, 20.0], "numinlets": 2, "id": "obj-116", "numoutlets": 1, "hidden": 1, "outlettype": [""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "if $i2 > 0 then $i1", "patching_rect": [380.0, 116.5, 91.0, 20.0], "numinlets": 2, "id": "obj-113", "numoutlets": 1, "hidden": 1, "outlettype": [""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "js spds.js", "patching_rect": [380.0, 148.5, 56.0, 20.0], "numinlets": 1, "id": "obj-106", "numoutlets": 2, "hidden": 1, "outlettype": ["", ""], "saved_object_attributes": {"filename": "novation.js", "parameter_enable": 0}}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "mid<PERSON><PERSON>e", "patching_rect": [380.0, 56.5, 92.5, 20.0], "numinlets": 1, "id": "obj-91", "numoutlets": 8, "hidden": 1, "outlettype": ["", "", "", "int", "int", "", "int", ""]}}, {"box": {"maxclass": "<PERSON><PERSON><PERSON>", "text": "midiin", "patching_rect": [380.0, 20.5, 40.0, 20.0], "numinlets": 1, "id": "obj-87", "numoutlets": 1, "hidden": 1, "outlettype": ["int"]}}], "lines": [{"patchline": {"source": ["obj-91", 0], "destination": ["obj-7", 0]}}, {"patchline": {"source": ["obj-87", 0], "destination": ["obj-91", 0], "order": 1}}, {"patchline": {"source": ["obj-87", 0], "destination": ["obj-5", 0], "order": 0}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-27", 0], "order": 1}}, {"patchline": {"source": ["obj-8", 0], "destination": ["obj-16", 1], "order": 0}}, {"patchline": {"source": ["obj-7", 0], "destination": ["obj-113", 0]}}, {"patchline": {"source": ["obj-7", 1], "destination": ["obj-113", 1]}}, {"patchline": {"source": ["obj-33", 0], "destination": ["obj-31", 1]}}, {"patchline": {"source": ["obj-31", 0], "destination": ["obj-27", 0]}}, {"patchline": {"source": ["obj-30", 0], "destination": ["obj-31", 0]}}, {"patchline": {"source": ["obj-27", 0], "destination": ["obj-21", 0]}}, {"patchline": {"source": ["obj-26", 0], "destination": ["obj-18", 0]}}, {"patchline": {"source": ["obj-25", 0], "destination": ["obj-20", 1]}}, {"patchline": {"source": ["obj-24", 0], "destination": ["obj-11", 0]}}, {"patchline": {"source": ["obj-22", 0], "destination": ["obj-122", 2], "order": 0}}, {"patchline": {"source": ["obj-22", 0], "destination": ["obj-1", 2], "order": 1}}, {"patchline": {"source": ["obj-21", 0], "destination": ["obj-22", 0], "order": 1}}, {"patchline": {"source": ["obj-21", 0], "destination": ["obj-17", 0], "order": 0}}, {"patchline": {"source": ["obj-20", 0], "destination": ["obj-122", 0]}}, {"patchline": {"source": ["obj-19", 0], "destination": ["obj-21", 0]}}, {"patchline": {"source": ["obj-18", 0], "destination": ["obj-24", 0]}}, {"patchline": {"source": ["obj-17", 0], "destination": ["obj-20", 0], "order": 1}}, {"patchline": {"source": ["obj-17", 0], "destination": ["obj-19", 0], "order": 0}}, {"patchline": {"source": ["obj-15", 0], "destination": ["obj-122", 1], "order": 0}}, {"patchline": {"source": ["obj-15", 0], "destination": ["obj-1", 1], "order": 1}}, {"patchline": {"source": ["obj-139", 0], "destination": ["obj-116", 0]}}, {"patchline": {"source": ["obj-135", 0], "destination": ["obj-17", 0], "order": 0}}, {"patchline": {"source": ["obj-135", 0], "destination": ["obj-13", 0], "order": 1}}, {"patchline": {"source": ["obj-134", 0], "destination": ["obj-17", 0], "order": 0}}, {"patchline": {"source": ["obj-134", 0], "destination": ["obj-15", 0], "order": 1}}, {"patchline": {"source": ["obj-13", 0], "destination": ["obj-122", 0], "order": 0}}, {"patchline": {"source": ["obj-13", 0], "destination": ["obj-1", 0], "order": 1}}, {"patchline": {"source": ["obj-12", 0], "destination": ["obj-11", 0]}}, {"patchline": {"source": ["obj-117", 0], "destination": ["obj-135", 0], "order": 1}}, {"patchline": {"source": ["obj-117", 0], "destination": ["obj-134", 0], "order": 0}}, {"patchline": {"source": ["obj-116", 0], "destination": ["obj-106", 0]}}, {"patchline": {"source": ["obj-113", 0], "destination": ["obj-30", 0], "order": 0}}, {"patchline": {"source": ["obj-113", 0], "destination": ["obj-27", 1], "order": 1}}, {"patchline": {"source": ["obj-113", 0], "destination": ["obj-106", 0], "order": 2}}, {"patchline": {"source": ["obj-11", 0], "destination": ["obj-106", 0]}}, {"patchline": {"source": ["obj-106", 0], "destination": ["obj-135", 0], "order": 1}}, {"patchline": {"source": ["obj-106", 0], "destination": ["obj-134", 0], "order": 0}}, {"patchline": {"source": ["obj-106", 1], "destination": ["obj-10", 2]}}, {"patchline": {"source": ["obj-10", 0], "destination": ["obj-5", 0]}}, {"patchline": {"source": ["obj-1", 0], "destination": ["obj-139", 0], "order": 1}}, {"patchline": {"source": ["obj-1", 0], "destination": ["obj-116", 0], "order": 0}}], "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box"}